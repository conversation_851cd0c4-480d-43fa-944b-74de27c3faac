>>> cc

".\..\BSP\BSP_LED\handler\Src\bsp_led_handler.c", line 747: Warning:  #188-D: enumerated type mixed with another type
              ret = p_led_handler->p_led_os_interface->rtos_queue_send(
                  ^
".\..\BSP\BSP_LED\handler\Src\bsp_led_handler.c", line 752: Warning:  #188-D: enumerated type mixed with another type
              ret = p_led_handler->p_led_os_interface->rtos_queue_send_fromisr(
                  ^
".\..\BSP\BSP_LED\handler\Src\bsp_led_handler.c", line 875: Warning:  #188-D: enumerated type mixed with another type
      led_handler_rtos_ret_code_t ret = p_led_handler->p_led_os_interface->rtos_queue_send(
                                        ^
".\..\BSP\BSP_LED\handler\Src\bsp_led_handler.c", line 922: Warning:  #188-D: enumerated type mixed with another type
          led_handler_rtos_ret_code_t ret = p_led_handler->p_led_os_interface->rtos_queue_receive(
                                            ^
.\..\BSP\BSP_LED\handler\Src\bsp_led_handler.c: 4 warnings, 0 errors
".\..\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.c", line 74: Warning:  #188-D: enumerated type mixed with another type
  static reg_matchup_t g_reg_matchup_table[REG_QUANTITY] = {0};
                                                            ^
".\..\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.c", line 231: Warning:  #186-D: pointless comparison of unsigned integer with zero
      CHECK_REG_PLACE(p_event->reg_num);
      ^
".\..\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.c", line 398: Warning:  #177-D: variable "pdata"  was declared but never referenced
      rs485_event_handler_private_data_t *pdata = p_handler->private_data;
                                          ^
".\..\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.c", line 486: Warning:  #177-D: variable "ret_code"  was declared but never referenced
      rs485_handler_ret_t ret_code = RS485_HANDLER_OK;
                          ^
".\..\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.c", line 101: Warning:  #177-D: function "save_reg_value"  was declared but never referenced
  static rs485_handler_ret_t save_reg_value(rs485_event_handler_private_data_t *p_private,
                             ^
".\..\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.c", line 104: Warning:  #177-D: function "process_reg_value"  was declared but never referenced
  static rs485_handler_ret_t process_reg_value(rs485_event_handler_private_data_t *p_private,
                             ^
.\..\Busbar_Voltage_Online_Monitor\RS485\handler\Src\rs485_event_handler.c: 6 warnings, 0 errors
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 42: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG"  (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 53: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ"  (declared at line 38)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/handler/Inc/bsp_adc_collect_xxx_handler.h", line 58: Warning:  #47-D: incompatible redefinition of macro "ADC_COLLECT_LOG_IRQ"  (declared at line 39)
  #define ADC_COLLECT_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
          ^
".\..\BSP\BSP_AD7606\handler\Src\bsp_adc_collect_xxx_handler.c", line 767: Warning:  #550-D: variable "ret_code"  was set but never used
      uint8_t ret_code;
              ^
.\..\BSP\BSP_AD7606\handler\Src\bsp_adc_collect_xxx_handler.c: 4 warnings, 0 errors
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 42: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG"  (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 53: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ"  (declared at line 38)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
".\..\BSP\BSP_AD7606\hal_driver\Src\ad7606_driver.c", line 455: Warning:  #177-D: function "bsp_ad7606_irq_callback_fun"  was declared but never referenced
  static ad7606_driver_ret_code_t bsp_ad7606_irq_callback_fun(void *param)
                                  ^
.\..\BSP\BSP_AD7606\hal_driver\Src\ad7606_driver.c: 3 warnings, 0 errors
"../MCU_Peripherals_Drivers/Inc/mcu_spi_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "MCU_SPI_DRIVER_DEBUG"  (declared at line 94 of "../SYSTEM/Inc/system_config.h")
  #define MCU_SPI_DRIVER_DEBUG(...)
          ^
".\..\MCU_Peripherals_Drivers\Src\mcu_spi_driver.c", line 505: Warning:  #111-D: statement is unreachable
          }break;
           ^
".\..\MCU_Peripherals_Drivers\Src\mcu_spi_driver.c", line 42: Warning:  #177-D: variable "g_spi_dynamic_allocation"  was declared but never referenced
  static dynamic_allocation_t g_spi_dynamic_allocation = 
                              ^
.\..\MCU_Peripherals_Drivers\Src\mcu_spi_driver.c: 3 warnings, 0 errors
".\..\Busbar_Voltage_Online_Monitor\Uart_dev\Src\uart_dev.c", line 221: Warning:  #223-D: function "uart_led_indicator_on_data_received" declared implicitly
                  uart_led_indicator_on_data_received();
                  ^
".\..\Busbar_Voltage_Online_Monitor\Uart_dev\Src\uart_dev.c", line 250: Warning:  #223-D: function "uart_led_indicator_on_data_received" declared implicitly
              uart_led_indicator_on_data_received();
              ^
.\..\Busbar_Voltage_Online_Monitor\Uart_dev\Src\uart_dev.c: 2 warnings, 0 errors
".\..\Busbar_Voltage_Online_Monitor\Flash\handler\Src\flash_event_fun.c", line 191: Warning:  #111-D: statement is unreachable
          }break;
           ^
".\..\Busbar_Voltage_Online_Monitor\Flash\handler\Src\flash_event_fun.c", line 215: Warning:  #177-D: variable "sys_info"  was declared but never referenced
      system_info_t sys_info;
                    ^
.\..\Busbar_Voltage_Online_Monitor\Flash\handler\Src\flash_event_fun.c: 2 warnings, 0 errors
"../MCU_Peripherals_Drivers/Inc/mcu_spi_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "MCU_SPI_DRIVER_DEBUG"  (declared at line 94 of "../SYSTEM/Inc/system_config.h")
  #define MCU_SPI_DRIVER_DEBUG(...)
          ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 42: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG"  (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 53: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ"  (declared at line 38)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/handler/Inc/bsp_adc_collect_xxx_handler.h", line 58: Warning:  #47-D: incompatible redefinition of macro "ADC_COLLECT_LOG_IRQ"  (declared at line 39)
  #define ADC_COLLECT_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
          ^
.\..\Core\Src\main.c: 4 warnings, 0 errors
".\..\Busbar_Voltage_Online_Monitor\Flash\handler\Src\flash_event_handler.c", line 226: Warning:  #223-D: function "flash_event_register_cb_init" declared implicitly
      if (FLASH_HANDLER_OK != flash_event_register_cb_init()) {
                              ^
.\..\Busbar_Voltage_Online_Monitor\Flash\handler\Src\flash_event_handler.c: 1 warning, 0 errors
"../MCU_Peripherals_Drivers/Inc/mcu_spi_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "MCU_SPI_DRIVER_DEBUG"  (declared at line 94 of "../SYSTEM/Inc/system_config.h")
  #define MCU_SPI_DRIVER_DEBUG(...)
          ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 42: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG"  (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 53: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ"  (declared at line 38)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/handler/Inc/bsp_adc_collect_xxx_handler.h", line 58: Warning:  #47-D: incompatible redefinition of macro "ADC_COLLECT_LOG_IRQ"  (declared at line 39)
  #define ADC_COLLECT_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
          ^
".\..\Core\Src\freertos.c", line 70: Warning:  #1295-D: Deprecated declaration config_elog - give arg types
  void config_elog();
       ^
.\..\Core\Src\freertos.c: 5 warnings, 0 errors
"../MCU_Peripherals_Drivers/Inc/mcu_spi_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "MCU_SPI_DRIVER_DEBUG"  (declared at line 94 of "../SYSTEM/Inc/system_config.h")
  #define MCU_SPI_DRIVER_DEBUG(...)
          ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 42: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG"  (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 53: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ"  (declared at line 38)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/handler/Inc/bsp_adc_collect_xxx_handler.h", line 58: Warning:  #47-D: incompatible redefinition of macro "ADC_COLLECT_LOG_IRQ"  (declared at line 39)
  #define ADC_COLLECT_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
          ^
".\..\SYSTEM\Src\system_adaption.c", line 756: Warning:  #550-D: variable "fft_calcu_adc_arg"  was set but never used
      static fft_calcu_adc_arg_t fft_calcu_adc_arg;
                                 ^
".\..\SYSTEM\Src\system_adaption.c", line 897: Warning:  #550-D: variable "ac_phase_data"  was set but never used
      static float ac_phase_data = 0.0f;
                   ^
".\..\SYSTEM\Src\system_adaption.c", line 1552: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, const uint8_t *, uint16_t, uint32_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t, uint32_t)"
                           HAL_UART_Transmit,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1553: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, const uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UART_Transmit_IT,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1554: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t, uint32_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t, uint32_t)"
                           HAL_UART_Receive,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1555: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UART_Receive_IT,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1556: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UARTEx_ReceiveToIdle_IT))
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1945: Warning:  #144-D: a value of type "void *(*)(const char *, uint32_t, _Bool, void *, void (*)(void *))" cannot be used to initialize an entity of type "rs485_os_ret_t (*)(const char *, uint32_t, void *, void (*)(void *), void **)"
          .rtos_timer_create  = (void*(*)(const char*, uint32_t, bool, void*, void(*)(void*)))os_timer_create,
                                ^
".\..\SYSTEM\Src\system_adaption.c", line 97: Warning:  #177-D: variable "g_adc_range_default"  was declared but never referenced
  static adc_collect_set_range_t g_adc_range_default = ADC_COLLECT_RANGE_5V;
                                 ^
.\..\SYSTEM\Src\system_adaption.c: 13 warnings, 0 errors
".\..\Tasks\Data_Process_Task\Src\data_process_task.c", line 135: Warning:  #940-D: missing return statement at end of non-void function "__data_proc_mount_handler" 
  }
  ^
".\..\Tasks\Data_Process_Task\Src\data_process_task.c", line 538: Warning:  #177-D: variable "ret"  was declared but never referenced
      data_proc_ret_t ret = DATA_PROC_OK;
                      ^
.\..\Tasks\Data_Process_Task\Src\data_process_task.c: 2 warnings, 0 errors
".\..\unit_test\Src\fft_result_test.c", line 6: Warning:  #177-D: variable "fft_result_data"  was declared but never referenced
      fft_result_event_t fft_result_data;
                         ^
.\..\unit_test\Src\fft_result_test.c: 1 warning, 0 errors
".\..\Tasks\Data_Process_Task\Src\data_proc_calcu_fun.c", line 187: Warning:  #177-D: variable "last_fft_avg_data"  was declared but never referenced
      static float last_fft_avg_data = 0.0f;
                   ^
".\..\Tasks\Data_Process_Task\Src\data_proc_calcu_fun.c", line 275: Warning:  #177-D: variable "voltage_data_types"  was declared but never referenced
      static const uint8_t voltage_data_types[] = {
                           ^
".\..\Tasks\Data_Process_Task\Src\data_proc_calcu_fun.c", line 282: Warning:  #177-D: variable "voltage_phase_data_types"  was declared but never referenced
      static const uint8_t voltage_phase_data_types[] = {
                           ^
".\..\Tasks\Data_Process_Task\Src\data_proc_calcu_fun.c", line 305: Warning:  #177-D: variable "voltage_name"  was declared but never referenced
      const char* voltage_name = voltage_names[voltage_type];
                  ^
".\..\Tasks\Data_Process_Task\Src\data_proc_calcu_fun.c", line 307: Warning:  #550-D: variable "ac_phase"  was set but never used
      static float ac_phase = 0.0f;
                   ^
.\..\Tasks\Data_Process_Task\Src\data_proc_calcu_fun.c: 5 warnings, 0 errors
".\..\Tasks\FFT_Task\Src\fft_calculate_task.c", line 330: Warning:  #550-D: variable "event_bit"  was set but never used
                              uint32_t event_bit = 0;
                                       ^
".\..\Tasks\FFT_Task\Src\fft_calculate_task.c", line 1240: Warning:  #177-D: variable "fft_data_property"  was declared but never referenced
      fft_data_property_t          fft_data_property;
                                   ^
".\..\Tasks\FFT_Task\Src\fft_calculate_task.c", line 1246: Warning:  #177-D: variable "result_event"  was declared but never referenced
      fft_result_event_t result_event;
                         ^
".\..\Tasks\FFT_Task\Src\fft_calculate_task.c", line 1583: Warning:  #177-D: variable "mag"  was declared but never referenced
      float32_t mag;
                ^
.\..\Tasks\FFT_Task\Src\fft_calculate_task.c: 4 warnings, 0 errors
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 42: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG"  (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 53: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ"  (declared at line 38)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
"../MCU_Peripherals_Drivers/Inc/mcu_spi_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "MCU_SPI_DRIVER_DEBUG"  (declared at line 94 of "../SYSTEM/Inc/system_config.h")
  #define MCU_SPI_DRIVER_DEBUG(...)
          ^
"../BSP/BSP_AD7606/handler/Inc/bsp_adc_collect_xxx_handler.h", line 58: Warning:  #47-D: incompatible redefinition of macro "ADC_COLLECT_LOG_IRQ"  (declared at line 39)
  #define ADC_COLLECT_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
          ^
".\..\unit_test\Src\ad7606_test.c", line 84: Warning:  #111-D: statement is unreachable
      xSemaphoreGiveFromISR(psemphore, NULL);
      ^
".\..\unit_test\Src\ad7606_test.c", line 145: Warning:  #191-D: type qualifier is meaningless on cast type
          (TaskHandle_t * const) task_handle))
           ^
".\..\unit_test\Src\ad7606_test.c", line 260: Warning:  #144-D: a value of type "HAL_StatusTypeDef (*)(SPI_HandleTypeDef *)" cannot be used to initialize an entity of type "mcu_spi_ret_code_t (*)(spi_handler_t *)"
          .pfspi_deinit    = HAL_SPI_DeInit,
                             ^
".\..\unit_test\Src\ad7606_test.c", line 261: Warning:  #144-D: a value of type "HAL_StatusTypeDef (*)(SPI_HandleTypeDef *, const uint8_t *, uint16_t, uint32_t)" cannot be used to initialize an entity of type "mcu_spi_ret_code_t (*)(spi_handler_t *, const uint8_t *, uint16_t, uint32_t)"
          .pfspi_write     = HAL_SPI_Transmit,
                             ^
".\..\unit_test\Src\ad7606_test.c", line 262: Warning:  #144-D: a value of type "HAL_StatusTypeDef (*)(SPI_HandleTypeDef *, uint8_t *, uint16_t, uint32_t)" cannot be used to initialize an entity of type "mcu_spi_ret_code_t (*)(spi_handler_t *, uint8_t *, uint16_t, uint32_t)"
          .pfspi_read      = HAL_SPI_Receive,
                             ^
".\..\unit_test\Src\ad7606_test.c", line 263: Warning:  #144-D: a value of type "HAL_StatusTypeDef (*)(SPI_HandleTypeDef *, const uint8_t *, uint16_t)" cannot be used to initialize an entity of type "mcu_spi_ret_code_t (*)(spi_handler_t *, const uint8_t *, uint16_t)"
  		.pfspi_write_irq = HAL_SPI_Transmit_IT,
  		                   ^
".\..\unit_test\Src\ad7606_test.c", line 264: Warning:  #144-D: a value of type "HAL_StatusTypeDef (*)(SPI_HandleTypeDef *, uint8_t *, uint16_t)" cannot be used to initialize an entity of type "mcu_spi_ret_code_t (*)(spi_handler_t *, uint8_t *, uint16_t)"
          .pfspi_read_irq  = HAL_SPI_Receive_IT,
                             ^
".\..\unit_test\Src\ad7606_test.c", line 265: Warning:  #144-D: a value of type "HAL_StatusTypeDef (*)(SPI_HandleTypeDef *, const uint8_t *, uint16_t)" cannot be used to initialize an entity of type "mcu_spi_ret_code_t (*)(spi_handler_t *, const uint8_t *, uint16_t)"
          .pfspi_write_dma = HAL_SPI_Transmit_DMA,
                             ^
".\..\unit_test\Src\ad7606_test.c", line 266: Warning:  #144-D: a value of type "HAL_StatusTypeDef (*)(SPI_HandleTypeDef *, uint8_t *, uint16_t)" cannot be used to initialize an entity of type "mcu_spi_ret_code_t (*)(spi_handler_t *, uint8_t *, uint16_t)"
          .pfspi_read_dma  = HAL_SPI_Receive_DMA,
                             ^
".\..\unit_test\Src\ad7606_test.c", line 270: Warning:  #144-D: a value of type "adc_collect_handler_os_ret_code_t (*)(void **)" cannot be used to initialize an entity of type "spi_rtos_ret_code_t (*)(void **const)"
          .rtos_semphorebinary_create = rtos_semphorebinary_create,
                                        ^
".\..\unit_test\Src\ad7606_test.c", line 271: Warning:  #144-D: a value of type "adc_collect_handler_os_ret_code_t (*)(void *)" cannot be used to initialize an entity of type "spi_rtos_ret_code_t (*)(void *const)"
          .rtos_semphore_delete       = rtos_semphore_delete,
                                        ^
".\..\unit_test\Src\ad7606_test.c", line 272: Warning:  #144-D: a value of type "adc_collect_handler_os_ret_code_t (*)(void *)" cannot be used to initialize an entity of type "spi_rtos_ret_code_t (*)(void *const)"
          .rtos_semphore_give         = rtos_semphore_give,
                                        ^
".\..\unit_test\Src\ad7606_test.c", line 273: Warning:  #144-D: a value of type "adc_collect_handler_os_ret_code_t (*)(void *)" cannot be used to initialize an entity of type "spi_rtos_ret_code_t (*)(void *const)"
          .rtos_semphore_give_formisr = rtos_semphore_give_formisr,
                                        ^
".\..\unit_test\Src\ad7606_test.c", line 274: Warning:  #144-D: a value of type "adc_collect_handler_os_ret_code_t (*)(void *, uint32_t)" cannot be used to initialize an entity of type "spi_rtos_ret_code_t (*)(void *const, uint32_t)"
          .rtos_semphore_take         = rtos_semphore_take,
                                        ^
".\..\unit_test\Src\ad7606_test.c", line 275: Warning:  #144-D: a value of type "adc_collect_handler_os_ret_code_t (*)(void *)" cannot be used to initialize an entity of type "spi_rtos_ret_code_t (*)(void *const)"
          .rtos_semphore_take_formisr = rtos_semphore_take_formisr,
                                        ^
".\..\unit_test\Src\ad7606_test.c", line 291: Warning:  #513-D: a value of type "spi_dev_ret_code_t (*)(struct spi_device_t *)" cannot be assigned to an entity of type "int8_t (*)(void *)"
      g_ad7606_spi_device.pfspi_dev_init = spi_device.pfspi_dev_init;
                                         ^
".\..\unit_test\Src\ad7606_test.c", line 292: Warning:  #513-D: a value of type "spi_dev_ret_code_t (*)(struct spi_device_t *)" cannot be assigned to an entity of type "int8_t (*)(void *)"
      g_ad7606_spi_device.pfspi_dev_deinit = spi_device.pfspi_dev_deinit;
                                           ^
".\..\unit_test\Src\ad7606_test.c", line 293: Warning:  #513-D: a value of type "spi_dev_ret_code_t (*)(struct spi_device_t *, uint8_t *, uint16_t, uint32_t)" cannot be assigned to an entity of type "int8_t (*)(void *, uint8_t *, uint16_t, uint32_t)"
      g_ad7606_spi_device.pfspi_dev_write = spi_device.pfspi_dev_write;
                                          ^
".\..\unit_test\Src\ad7606_test.c", line 294: Warning:  #513-D: a value of type "spi_dev_ret_code_t (*)(struct spi_device_t *, uint8_t *, uint16_t, uint32_t)" cannot be assigned to an entity of type "int8_t (*)(void *, uint8_t *, uint16_t, uint32_t)"
      g_ad7606_spi_device.pfspi_dev_read = spi_device.pfspi_dev_read;
                                         ^
.\..\unit_test\Src\ad7606_test.c: 23 warnings, 0 errors

>>> ld

Program Size: Code=125576 RO-data=34724 RW-data=1744 ZI-data=91504  
