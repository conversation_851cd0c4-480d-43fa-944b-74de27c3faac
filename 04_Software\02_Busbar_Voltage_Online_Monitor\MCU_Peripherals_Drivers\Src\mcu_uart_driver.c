#include <string.h>
#include <stdio.h>

#include "mcu_uart_driver.h"

static uart_handle_t g_uart_handle =
{
		.uart_msg        = NULL,
		.uartinit        = NULL,
		.uartdeinit      = NULL,
		.uartsend        = NULL,
		.uartsend_irq    = NULL,
		.uartreceive_irq = NULL,
		.uartreceive     = NULL,
};

/**
 * @brief 注册串口功能
 * 
 * @param uart_msg 串口消息结构体
 * @param uartinit 串口初始化函数
 * @param uartdeinit 	串口去初始化函数
 * @param uartsend	 	串口发送函数
 * @param uartsend_irq 	串口发送中断函数
 * @param uartreceive 	串口接收函数
 * @param uartreceive_irq 串口接收中断函数
 * @return int8_t -1表示失败，0表示成功
 */
// 声明一个函数，用于注册UART功能
int8_t register_uart_funtion( uart_msg_t uart_msg,
							 void (*uartinit)(void),
							 void (*uartdeinit)(void),
                             int8_t (*uartsend)(void* uart_struct, uint8_t* pData, uint16_t size, uint32_t timeout),
                             int8_t (*uartsend_irq)(void* uart_struct, uint8_t* pData, uint16_t size),
							 int8_t (*uartreceive)(void* uart_struct, uint8_t* pData, uint16_t size, uint32_t timeout),
                             int8_t (*uartreceive_irq)(void* uart_struct, uint8_t* pData, uint16_t size),
							int8_t(*uartreceiveidle_irq)(void* uart_struct, uint8_t* pData, uint16_t size)
                           )
{
	// 将参数赋值给全局变量g_uart_handle
	g_uart_handle.uart_msg 			= uart_msg;
	g_uart_handle.uartinit 			= uartinit;
	g_uart_handle.uartdeinit	 	= uartdeinit;
	g_uart_handle.uartsend 			= uartsend;
	g_uart_handle.uartsend_irq 		= uartsend_irq;
	g_uart_handle.uartreceive 		= uartreceive;
	g_uart_handle.uartreceive_irq 	= uartreceive_irq;
	g_uart_handle.uartreceiveidle_irq = uartreceiveidle_irq;
	// 如果全局变量g_uart_handle中的所有成员都不为空，则返回0
	if ((g_uart_handle.uart_msg.ps_uart        != NULL) &&
		(g_uart_handle.uartinit                != NULL) &&
		(g_uart_handle.uartsend                != NULL) &&
		(g_uart_handle.uartsend_irq            != NULL) &&
		(g_uart_handle.uartreceive             != NULL) &&
		(g_uart_handle.uartreceive_irq         != NULL) &&
		(g_uart_handle.uartreceiveidle_irq     != NULL)
		)
	{
		return 0;
	}
	return -1;
}


/**
 * @brief Get the uarthandle object
 * 		
 * @return puart_handle_t 
 */
puart_handle_t get_uarthandle(void)
{
	// 返回全局串口句柄
	return &g_uart_handle;
}