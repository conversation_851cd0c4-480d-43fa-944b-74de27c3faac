<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\build\Busbar_Voltage_Online_Monitor\Busbar_Voltage_Online_Monitor.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\build\Busbar_Voltage_Online_Monitor\Busbar_Voltage_Online_Monitor.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Sat Aug 02 16:25:44 2025
<BR><P>
<H3>Maximum Stack Usage =        856 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
fft_event_handler_thread &rArr; sync_fft_calculate &rArr; fft_calcuate_3rd &rArr; fft_calculate_window &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[6a]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6a]">ADC_IRQHandler</a><BR>
 <LI><a href="#[195]">elog_output</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[195]">elog_output</a><BR>
 <LI><a href="#[23c]">elog_strcpy</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[195]">elog_output</a><BR>
 <LI><a href="#[208]">elog_set_output_enabled</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[195]">elog_output</a><BR>
 <LI><a href="#[234]">elog_get_filter_tag_lvl</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[195]">elog_output</a><BR>
 <LI><a href="#[23d]">get_fmt_enabled</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[195]">elog_output</a><BR>
 <LI><a href="#[117]">arm_quick_sort_core_f32</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[117]">arm_quick_sort_core_f32</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[6a]">ADC_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[52]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[63]">DMA1_Stream0_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[64]">DMA1_Stream1_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[65]">DMA1_Stream2_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[66]">DMA1_Stream3_IRQHandler</a> from stm32f4xx_it.o(i.DMA1_Stream3_IRQHandler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[67]">DMA1_Stream4_IRQHandler</a> from stm32f4xx_it.o(i.DMA1_Stream4_IRQHandler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[68]">DMA1_Stream5_IRQHandler</a> from stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[69]">DMA1_Stream6_IRQHandler</a> from stm32f4xx_it.o(i.DMA1_Stream6_IRQHandler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[7e]">DMA1_Stream7_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[82]">DMA2_Stream0_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[83]">DMA2_Stream1_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[84]">DMA2_Stream2_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[85]">DMA2_Stream3_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[86]">DMA2_Stream4_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[88]">DMA2_Stream5_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[89]">DMA2_Stream6_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[8a]">DMA2_Stream7_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[55]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[5e]">EXTI0_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[7b]">EXTI15_10_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[5f]">EXTI1_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[60]">EXTI2_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[61]">EXTI3_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[62]">EXTI4_IRQHandler</a> from stm32f4xx_it.o(i.EXTI4_IRQHandler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[6b]">EXTI9_5_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[5c]">FLASH_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[8e]">FPU_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[e]">HAL_GPIO_DeInit</a> from stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[10]">HAL_GPIO_ReadPin</a> from stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[11]">HAL_GPIO_TogglePin</a> from stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[f]">HAL_GPIO_WritePin</a> from stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[25]">HAL_GetTick</a> from stm32f4xx_hal.o(i.HAL_GetTick) referenced 4 times from system_adaption.o(.data)
 <LI><a href="#[25]">HAL_GetTick</a> from stm32f4xx_hal.o(i.HAL_GetTick) referenced from system_adaption.o(i.led_handler_inst)
 <LI><a href="#[31]">HAL_SPI_DeInit</a> from stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[33]">HAL_SPI_Receive</a> from stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[37]">HAL_SPI_Receive_DMA</a> from stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[35]">HAL_SPI_Receive_IT</a> from stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[32]">HAL_SPI_Transmit</a> from stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[36]">HAL_SPI_Transmit_DMA</a> from stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[34]">HAL_SPI_Transmit_IT</a> from stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[e7]">HAL_UARTEx_ReceiveToIdle_IT</a> from stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) referenced from system_adaption.o(i.uart_dev_register)
 <LI><a href="#[e9]">HAL_UART_Receive</a> from stm32f4xx_hal_uart.o(i.HAL_UART_Receive) referenced from system_adaption.o(i.uart_dev_register)
 <LI><a href="#[e8]">HAL_UART_Receive_IT</a> from stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) referenced from system_adaption.o(i.uart_dev_register)
 <LI><a href="#[eb]">HAL_UART_Transmit</a> from stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) referenced from system_adaption.o(i.uart_dev_register)
 <LI><a href="#[ea]">HAL_UART_Transmit_IT</a> from stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT) referenced from system_adaption.o(i.uart_dev_register)
 <LI><a href="#[50]">HardFault_Handler</a> from cmb_fault.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[74]">I2C1_ER_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[73]">I2C1_EV_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[76]">I2C2_ER_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[75]">I2C2_EV_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[8d]">I2C3_ER_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[8c]">I2C3_EV_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[d]">MX_GPIO_Init</a> from gpio.o(i.MX_GPIO_Init) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[ec]">MX_USART2_UART_Init</a> from usart.o(i.MX_USART2_UART_Init) referenced from system_adaption.o(i.uart_dev_register)
 <LI><a href="#[51]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[4f]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[87]">OTG_FS_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[7d]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[59]">PVD_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[56]">PendSV_Handler</a> from port.o(.emb_text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[5d]">RCC_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[7c]">RTC_Alarm_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[5b]">RTC_WKUP_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[4e]">Reset_Handler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[3]">Rs485DeviceInit</a> from rs485_dev.o(i.Rs485DeviceInit) referenced 2 times from rs485_dev.o(.data)
 <LI><a href="#[5]">Rs485_Recv</a> from rs485_dev.o(i.Rs485_Recv) referenced 2 times from rs485_dev.o(.data)
 <LI><a href="#[4]">Rs485_Send</a> from rs485_dev.o(i.Rs485_Send) referenced 2 times from rs485_dev.o(.data)
 <LI><a href="#[7f]">SDIO_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[77]">SPI1_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[78]">SPI2_IRQHandler</a> from stm32f4xx_it.o(i.SPI2_IRQHandler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[81]">SPI3_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[8f]">SPI4_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[90]">SPI5_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[9c]">SPI_2linesRxISR_16BIT</a> from stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT)
 <LI><a href="#[9e]">SPI_2linesRxISR_8BIT</a> from stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT)
 <LI><a href="#[9d]">SPI_2linesTxISR_16BIT</a> from stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT)
 <LI><a href="#[9f]">SPI_2linesTxISR_8BIT</a> from stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT)
 <LI><a href="#[94]">SPI_DMAAbortOnError</a> from stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler)
 <LI><a href="#[97]">SPI_DMAError</a> from stm32f4xx_hal_spi.o(i.SPI_DMAError) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA)
 <LI><a href="#[97]">SPI_DMAError</a> from stm32f4xx_hal_spi.o(i.SPI_DMAError) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA)
 <LI><a href="#[97]">SPI_DMAError</a> from stm32f4xx_hal_spi.o(i.SPI_DMAError) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA)
 <LI><a href="#[95]">SPI_DMAHalfReceiveCplt</a> from stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA)
 <LI><a href="#[95]">SPI_DMAHalfReceiveCplt</a> from stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA)
 <LI><a href="#[a0]">SPI_DMAHalfTransmitCplt</a> from stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA)
 <LI><a href="#[9a]">SPI_DMAHalfTransmitReceiveCplt</a> from stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA)
 <LI><a href="#[96]">SPI_DMAReceiveCplt</a> from stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA)
 <LI><a href="#[96]">SPI_DMAReceiveCplt</a> from stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA)
 <LI><a href="#[a1]">SPI_DMATransmitCplt</a> from stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA)
 <LI><a href="#[9b]">SPI_DMATransmitReceiveCplt</a> from stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA)
 <LI><a href="#[98]">SPI_RxISR_16BIT</a> from stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT)
 <LI><a href="#[99]">SPI_RxISR_8BIT</a> from stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT)
 <LI><a href="#[a2]">SPI_TxISR_16BIT</a> from stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT)
 <LI><a href="#[a3]">SPI_TxISR_8BIT</a> from stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) referenced from stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT)
 <LI><a href="#[54]">SVC_Handler</a> from port.o(.emb_text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[1]">Select_UartDev</a> from rs485_dev.o(i.Select_UartDev) referenced 2 times from rs485_dev.o(.data)
 <LI><a href="#[2]">SetRs485DeviceMode</a> from rs485_dev.o(i.SetRs485DeviceMode) referenced 2 times from rs485_dev.o(.data)
 <LI><a href="#[a5]">StartDefaultTask</a> from freertos.o(i.StartDefaultTask) referenced from freertos.o(i.MX_FREERTOS_Init)
 <LI><a href="#[57]">SysTick_Handler</a> from cmsis_os2.o(i.SysTick_Handler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[92]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f411xe.o(.text)
 <LI><a href="#[5a]">TAMP_STAMP_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[6c]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[6f]">TIM1_CC_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[6e]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[6d]">TIM1_UP_TIM10_IRQHandler</a> from stm32f4xx_it.o(i.TIM1_UP_TIM10_IRQHandler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[70]">TIM2_IRQHandler</a> from stm32f4xx_it.o(i.TIM2_IRQHandler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[71]">TIM3_IRQHandler</a> from stm32f4xx_it.o(i.TIM3_IRQHandler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[72]">TIM4_IRQHandler</a> from stm32f4xx_it.o(i.TIM4_IRQHandler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[80]">TIM5_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[a4]">UART_DMAAbortOnError</a> from stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) referenced from stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
 <LI><a href="#[79]">USART1_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[7a]">USART2_IRQHandler</a> from stm32f4xx_it.o(i.USART2_IRQHandler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[8b]">USART6_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[53]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[58]">WWDG_IRQHandler</a> from startup_stm32f411xe.o(.text) referenced from startup_stm32f411xe.o(RESET)
 <LI><a href="#[b7]">__adc_collect_event_thread_notify</a> from bsp_adc_collect_xxx_handler.o(i.__adc_collect_event_thread_notify) referenced from bsp_adc_collect_xxx_handler.o(i.bsp_adc_collect_xxx_handler_init)
 <LI><a href="#[93]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f411xe.o(.text)
 <LI><a href="#[cf]">__read_reg_data_callbackfun</a> from reg_adress_msg.o(i.__read_reg_data_callbackfun) referenced from reg_adress_msg.o(i.modbus_read_reg)
 <LI><a href="#[a7]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0snprintf)
 <LI><a href="#[a7]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0vsnprintf)
 <LI><a href="#[a8]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[ab]">ad7606_busy_irq_callback_fun</a> from ad7606_driver.o(i.ad7606_busy_irq_callback_fun) referenced from ad7606_driver.o(i.ad7606_init)
 <LI><a href="#[ae]">ad7606_deinit</a> from ad7606_driver.o(i.ad7606_deinit) referenced from ad7606_driver.o(i.bsp_ad7606_driver_instance)
 <LI><a href="#[ad]">ad7606_init</a> from ad7606_driver.o(i.ad7606_init) referenced from ad7606_driver.o(i.bsp_ad7606_driver_instance)
 <LI><a href="#[b0]">ad7606_read_busy</a> from ad7606_driver.o(i.ad7606_read_busy) referenced from ad7606_driver.o(i.bsp_ad7606_driver_instance)
 <LI><a href="#[b2]">ad7606_read_data</a> from ad7606_driver.o(i.ad7606_read_data) referenced from ad7606_driver.o(i.bsp_ad7606_driver_instance)
 <LI><a href="#[b1]">ad7606_reset</a> from ad7606_driver.o(i.ad7606_reset) referenced from ad7606_driver.o(i.bsp_ad7606_driver_instance)
 <LI><a href="#[b3]">ad7606_set_sampling_mode</a> from ad7606_driver.o(i.ad7606_set_sampling_mode) referenced from ad7606_driver.o(i.bsp_ad7606_driver_instance)
 <LI><a href="#[b4]">ad7606_set_sampling_range</a> from ad7606_driver.o(i.ad7606_set_sampling_range) referenced from ad7606_driver.o(i.bsp_ad7606_driver_instance)
 <LI><a href="#[aa]">ad7606_spi_rx_dma_callback_fun</a> from ad7606_driver.o(i.ad7606_spi_rx_dma_callback_fun) referenced from ad7606_driver.o(i.ad7606_init)
 <LI><a href="#[af]">ad7606_startconvst</a> from ad7606_driver.o(i.ad7606_startconvst) referenced from ad7606_driver.o(i.bsp_ad7606_driver_instance)
 <LI><a href="#[ac]">adc_collect_event_thread</a> from bsp_adc_collect_xxx_handler.o(i.adc_collect_event_thread) referenced from system_adaption.o(i.adc_collect_handler_source_inst)
 <LI><a href="#[b6]">adc_collect_irq_callback</a> from bsp_adc_collect_xxx_handler.o(i.adc_collect_irq_callback) referenced from bsp_adc_collect_xxx_handler.o(i.bsp_adc_collect_xxx_handler_init)
 <LI><a href="#[b5]">adc_collect_set_event_thread</a> from bsp_adc_collect_xxx_handler.o(i.adc_collect_set_event_thread) referenced from bsp_adc_collect_xxx_handler.o(i.bsp_adc_collect_xxx_handler_init)
 <LI><a href="#[c0]">async_output</a> from elog_async.o(i.async_output) referenced from elog_async.o(i.elog_async_init)
 <LI><a href="#[45]">bsp_adc_collect_xxxx_disable_peripheral_clock</a> from system_adaption.o(i.bsp_adc_collect_xxxx_disable_peripheral_clock) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[44]">bsp_adc_collect_xxxx_enable_peripheral_clock</a> from system_adaption.o(i.bsp_adc_collect_xxxx_enable_peripheral_clock) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[42]">bsp_adc_collect_xxxx_interrupt_deinit</a> from system_adaption.o(i.bsp_adc_collect_xxxx_interrupt_deinit) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[43]">bsp_adc_collect_xxxx_interrupt_enable</a> from system_adaption.o(i.bsp_adc_collect_xxxx_interrupt_enable) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[41]">bsp_adc_collect_xxxx_interrupt_init</a> from system_adaption.o(i.bsp_adc_collect_xxxx_interrupt_init) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[ba]">bsp_led_driver_control</a> from bsp_led_driver.o(i.bsp_led_driver_control) referenced from bsp_led_driver.o(i.bsp_led_driver_inst)
 <LI><a href="#[b9]">bsp_led_driver_deinit</a> from bsp_led_driver.o(i.bsp_led_driver_deinit) referenced from bsp_led_driver.o(i.bsp_led_driver_inst)
 <LI><a href="#[bc]">bsp_led_driver_get_led_which</a> from bsp_led_driver.o(i.bsp_led_driver_get_led_which) referenced from bsp_led_driver.o(i.bsp_led_driver_inst)
 <LI><a href="#[b8]">bsp_led_driver_init</a> from bsp_led_driver.o(i.bsp_led_driver_init) referenced from bsp_led_driver.o(i.bsp_led_driver_inst)
 <LI><a href="#[bb]">bsp_led_driver_set_brightness</a> from bsp_led_driver.o(i.bsp_led_driver_set_brightness) referenced from bsp_led_driver.o(i.bsp_led_driver_inst)
 <LI><a href="#[bf]">data_proc_handler_thread</a> from data_process_task.o(i.data_proc_handler_thread) referenced from system_adaption.o(i.data_process_task_instance)
 <LI><a href="#[17]">data_proc_register_cb_init</a> from data_proc_calcu_fun.o(i.data_proc_register_cb_init) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[27]">fft_check_data_valid</a> from system_adaption.o(i.fft_check_data_valid) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[c1]">fft_collection_data_thread</a> from fft_calculate_task.o(i.fft_collection_data_thread) referenced from fft_calculate_task.o(i.fft_calcu_handler_init)
 <LI><a href="#[c2]">fft_event_handler_thread</a> from fft_calculate_task.o(i.fft_event_handler_thread) referenced from system_adaption.o(i.fft_calculate_task_instance)
 <LI><a href="#[26]">fft_get_collect_data</a> from system_adaption.o(i.fft_get_collect_data) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[28]">fft_get_collect_data_limit</a> from system_adaption.o(i.fft_get_collect_data_limit) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[29]">fft_result_data_send</a> from system_adaption.o(i.fft_result_data_send) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[c3]">flash_event_handler_thread</a> from flash_event_handler.o(i.flash_event_handler_thread) referenced from flash_event_handler.o(i.flash_event_handler_init)
 <LI><a href="#[4d]">flash_event_register_cb_init</a> from flash_event_fun.o(i.flash_event_register_cb_init) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[4c]">flash_read_rs485_reg_value</a> from system_adaption.o(i.flash_read_rs485_reg_value) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[4b]">flash_save_rs485_reg_value</a> from system_adaption.o(i.flash_save_rs485_reg_value) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[a6]">fputc</a> from main.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[d6]">get_buff_adress</a> from ringbuff.o(i.get_buff_adress) referenced from ringbuff.o(i.ring_buff_instance)
 <LI><a href="#[c7]">gpio_deinit</a> from mcu_gpio_driver.o(i.gpio_deinit) referenced from mcu_gpio_driver.o(i.gpio_dev_instance)
 <LI><a href="#[c6]">gpio_init</a> from mcu_gpio_driver.o(i.gpio_init) referenced from mcu_gpio_driver.o(i.gpio_dev_instance)
 <LI><a href="#[c9]">gpio_read_pin</a> from mcu_gpio_driver.o(i.gpio_read_pin) referenced from mcu_gpio_driver.o(i.gpio_dev_instance)
 <LI><a href="#[ca]">gpio_toggle_pin</a> from mcu_gpio_driver.o(i.gpio_toggle_pin) referenced from mcu_gpio_driver.o(i.gpio_dev_instance)
 <LI><a href="#[c8]">gpio_write_pin</a> from mcu_gpio_driver.o(i.gpio_write_pin) referenced from mcu_gpio_driver.o(i.gpio_dev_instance)
 <LI><a href="#[c5]">handle_read_sys_info</a> from flash_event_fun.o(i.handle_read_sys_info) referenced from flash_event_fun.o(i.flash_event_register_cb_init)
 <LI><a href="#[c4]">handle_save_sys_info</a> from flash_event_fun.o(i.handle_save_sys_info) referenced from flash_event_fun.o(i.flash_event_register_cb_init)
 <LI><a href="#[be]">handle_voltage_data</a> from data_proc_calcu_fun.o(i.handle_voltage_data) referenced from data_proc_calcu_fun.o(i.data_proc_register_cb_init)
 <LI><a href="#[c]">internal_flash_erase</a> from mcu_flash_driver.o(i.internal_flash_erase) referenced 2 times from mcu_flash_driver.o(.data)
 <LI><a href="#[9]">internal_flash_init</a> from mcu_flash_driver.o(i.internal_flash_init) referenced 2 times from mcu_flash_driver.o(.data)
 <LI><a href="#[b]">internal_flash_read</a> from mcu_flash_driver.o(i.internal_flash_read) referenced 2 times from mcu_flash_driver.o(.data)
 <LI><a href="#[a]">internal_flash_write</a> from mcu_flash_driver.o(i.internal_flash_write) referenced 2 times from mcu_flash_driver.o(.data)
 <LI><a href="#[bd]">led_handler_task</a> from bsp_led_handler.o(i.led_handler_task) referenced from bsp_led_handler.o(i.bsp_led_handler_instance)
 <LI><a href="#[cc]">lpf_fir</a> from lpf_fir_alogorithm.o(i.lpf_fir) referenced from lpf_fir_alogorithm.o(i.lpf_fir_instance)
 <LI><a href="#[cb]">lpf_fir_init</a> from lpf_fir_alogorithm.o(i.lpf_fir_init) referenced from lpf_fir_alogorithm.o(i.lpf_fir_instance)
 <LI><a href="#[cd]">lpf_iir</a> from lpf_fir_alogorithm.o(i.lpf_iir) referenced from lpf_fir_alogorithm.o(i.lpf_fir_instance)
 <LI><a href="#[91]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[2e]">mcu_spi_cs_high</a> from system_adaption.o(i.mcu_spi_cs_high) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[2f]">mcu_spi_cs_low</a> from system_adaption.o(i.mcu_spi_cs_low) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[ce]">modbus_switch_recv_status</a> from modbus_rtu.o(i.modbus_switch_recv_status) referenced from modbus_rtu.o(i.modbus_init)
 <LI><a href="#[2a]">os_critical_enter</a> from os_critical.o(i.os_critical_enter) referenced 4 times from system_adaption.o(.data)
 <LI><a href="#[2a]">os_critical_enter</a> from os_critical.o(i.os_critical_enter) referenced from system_adaption.o(i.led_handler_inst)
 <LI><a href="#[46]">os_critical_enter_isr</a> from os_critical.o(i.os_critical_enter_isr) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[2b]">os_critical_exit</a> from os_critical.o(i.os_critical_exit) referenced 4 times from system_adaption.o(.data)
 <LI><a href="#[2b]">os_critical_exit</a> from os_critical.o(i.os_critical_exit) referenced from system_adaption.o(i.led_handler_inst)
 <LI><a href="#[47]">os_critical_exit_isr</a> from os_critical.o(i.os_critical_exit_isr) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[23]">os_event_group_clear_bits</a> from os_event_group.o(i.os_event_group_clear_bits) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[20]">os_event_group_create</a> from os_event_group.o(i.os_event_group_create) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[21]">os_event_group_delete</a> from os_event_group.o(i.os_event_group_delete) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[22]">os_event_group_set_bits</a> from os_event_group.o(i.os_event_group_set_bits) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[24]">os_event_group_wait_bits</a> from os_event_group.o(i.os_event_group_wait_bits) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[3e]">os_mutex_create</a> from os_semaphore.o(i.os_mutex_create) referenced 4 times from system_adaption.o(.data)
 <LI><a href="#[12]">os_queue_create</a> from os_queue.o(i.os_queue_create) referenced 12 times from system_adaption.o(.data)
 <LI><a href="#[13]">os_queue_delete</a> from os_queue.o(i.os_queue_delete) referenced 12 times from system_adaption.o(.data)
 <LI><a href="#[15]">os_queue_receive</a> from os_queue.o(i.os_queue_receive) referenced 12 times from system_adaption.o(.data)
 <LI><a href="#[14]">os_queue_send</a> from os_queue.o(i.os_queue_send) referenced 12 times from system_adaption.o(.data)
 <LI><a href="#[19]">os_semaphore_binary_create</a> from os_semaphore.o(i.os_semaphore_binary_create) referenced 6 times from system_adaption.o(.data)
 <LI><a href="#[1a]">os_semaphore_delete</a> from os_semaphore.o(i.os_semaphore_delete) referenced 8 times from system_adaption.o(.data)
 <LI><a href="#[38]">os_semaphore_give</a> from os_semaphore.o(i.os_semaphore_give) referenced 6 times from system_adaption.o(.data)
 <LI><a href="#[1c]">os_semaphore_give_fromisr</a> from os_semaphore.o(i.os_semaphore_give_fromisr) referenced 6 times from system_adaption.o(.data)
 <LI><a href="#[1b]">os_semaphore_take</a> from os_semaphore.o(i.os_semaphore_take) referenced 8 times from system_adaption.o(.data)
 <LI><a href="#[39]">os_semaphore_take_fromisr</a> from os_semaphore.o(i.os_semaphore_take_fromisr) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[18]">os_task_create</a> from os_task.o(i.os_task_create) referenced 10 times from system_adaption.o(.data)
 <LI><a href="#[4a]">os_task_delete</a> from os_task.o(i.os_task_delete) referenced 4 times from system_adaption.o(.data)
 <LI><a href="#[1f]">os_task_get_handle</a> from os_task.o(i.os_task_get_handle) referenced 4 times from system_adaption.o(.data)
 <LI><a href="#[40]">os_task_notify_clear</a> from os_task.o(i.os_task_notify_clear) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[1d]">os_task_notify_give</a> from os_task.o(i.os_task_notify_give) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[3f]">os_task_notify_give_fromisr</a> from os_task.o(i.os_task_notify_give_fromisr) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[1e]">os_task_notify_take</a> from os_task.o(i.os_task_notify_take) referenced 4 times from system_adaption.o(.data)
 <LI><a href="#[3a]">os_timer_create</a> from os_timer.o(i.os_timer_create) referenced 4 times from system_adaption.o(.data)
 <LI><a href="#[3b]">os_timer_delete</a> from os_timer.o(i.os_timer_delete) referenced 4 times from system_adaption.o(.data)
 <LI><a href="#[3c]">os_timer_start_fromisr</a> from os_timer.o(i.os_timer_start_fromisr) referenced 4 times from system_adaption.o(.data)
 <LI><a href="#[3d]">os_timer_stop_fromisr</a> from os_timer.o(i.os_timer_stop_fromisr) referenced 4 times from system_adaption.o(.data)
 <LI><a href="#[ed]">prvIdleTask</a> from tasks.o(i.prvIdleTask) referenced from tasks.o(i.vTaskStartScheduler)
 <LI><a href="#[d0]">prvTaskExitError</a> from port.o(i.prvTaskExitError) referenced from port.o(i.pxPortInitialiseStack)
 <LI><a href="#[ee]">prvTimerTask</a> from timers.o(i.prvTimerTask) referenced from timers.o(i.xTimerCreateTimerTask)
 <LI><a href="#[2c]">pvPortMalloc</a> from heap_4.o(i.pvPortMalloc) referenced 8 times from system_adaption.o(.data)
 <LI><a href="#[2c]">pvPortMalloc</a> from heap_4.o(i.pvPortMalloc) referenced from system_adaption.o(i.led_handler_inst)
 <LI><a href="#[d2]">ring_buff_deinit</a> from ringbuff.o(i.ring_buff_deinit) referenced from ringbuff.o(i.ring_buff_instance)
 <LI><a href="#[d7]">ring_buff_dma_writes_index</a> from ringbuff.o(i.ring_buff_dma_writes_index) referenced from ringbuff.o(i.ring_buff_instance)
 <LI><a href="#[d8]">ring_buff_get_item_size</a> from ringbuff.o(i.ring_buff_get_item_size) referenced from ringbuff.o(i.ring_buff_instance)
 <LI><a href="#[d1]">ring_buff_init</a> from ringbuff.o(i.ring_buff_init) referenced from ringbuff.o(i.ring_buff_instance)
 <LI><a href="#[d4]">ring_buff_read</a> from ringbuff.o(i.ring_buff_read) referenced from ringbuff.o(i.ring_buff_instance)
 <LI><a href="#[d5]">ring_buff_reset</a> from ringbuff.o(i.ring_buff_reset) referenced from ringbuff.o(i.ring_buff_instance)
 <LI><a href="#[d3]">ring_buff_write</a> from ringbuff.o(i.ring_buff_write) referenced from ringbuff.o(i.ring_buff_instance)
 <LI><a href="#[dc]">rs485_event_handle_thread</a> from rs485_event_handler.o(i.rs485_event_handle_thread) referenced from rs485_event_handler.o(i.rs485_event_handler_init)
 <LI><a href="#[d9]">rs485_handle_collect_data_input</a> from rs485_event_handler.o(i.rs485_handle_collect_data_input) referenced from rs485_event_handler.o(i.rs485_event_fun_init)
 <LI><a href="#[da]">rs485_handle_read_reg_data</a> from rs485_event_handler.o(i.rs485_handle_read_reg_data) referenced from rs485_event_handler.o(i.rs485_event_fun_init)
 <LI><a href="#[db]">rs485_handle_write_reg_data</a> from rs485_event_handler.o(i.rs485_handle_write_reg_data) referenced from rs485_event_handler.o(i.rs485_event_fun_init)
 <LI><a href="#[dd]">rs485_output_msg_thread</a> from rs485_event_handler.o(i.rs485_output_msg_thread) referenced from rs485_event_handler.o(i.rs485_event_handler_init)
 <LI><a href="#[e2]">spi_dev_deinit</a> from mcu_spi_driver.o(i.spi_dev_deinit) referenced from mcu_spi_driver.o(i.spi_dev_instance)
 <LI><a href="#[30]">spi_dev_init</a> from system_adaption.o(i.spi_dev_init) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[e1]">spi_dev_init</a> from mcu_spi_driver.o(i.spi_dev_init) referenced from mcu_spi_driver.o(i.spi_dev_instance)
 <LI><a href="#[e4]">spi_dev_read</a> from mcu_spi_driver.o(i.spi_dev_read) referenced from mcu_spi_driver.o(i.spi_dev_instance)
 <LI><a href="#[e5]">spi_dev_read_dma</a> from mcu_spi_driver.o(i.spi_dev_read_dma) referenced from mcu_spi_driver.o(i.spi_dev_instance)
 <LI><a href="#[e6]">spi_dev_register_callback</a> from mcu_spi_driver.o(i.spi_dev_register_callback) referenced from mcu_spi_driver.o(i.spi_dev_instance)
 <LI><a href="#[e0]">spi_dev_timer_callback</a> from mcu_spi_driver.o(i.spi_dev_timer_callback) referenced from mcu_spi_driver.o(i.spi_dev_init)
 <LI><a href="#[e3]">spi_dev_write</a> from mcu_spi_driver.o(i.spi_dev_write) referenced from mcu_spi_driver.o(i.spi_dev_instance)
 <LI><a href="#[df]">spi_interrupt_rxcallbackfun</a> from mcu_spi_driver.o(i.spi_interrupt_rxcallbackfun) referenced from mcu_spi_driver.o(i.spi_dev_init)
 <LI><a href="#[de]">spi_interrupt_txcallbackfun</a> from mcu_spi_driver.o(i.spi_interrupt_txcallbackfun) referenced from mcu_spi_driver.o(i.spi_dev_init)
 <LI><a href="#[48]">timer_start_collect_data</a> from system_adaption.o(i.timer_start_collect_data) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[49]">timer_stop_collect_data</a> from system_adaption.o(i.timer_stop_collect_data) referenced 2 times from system_adaption.o(.data)
 <LI><a href="#[6]">uart_init</a> from uart_dev.o(i.uart_init) referenced 2 times from uart_dev.o(.data)
 <LI><a href="#[a9]">uart_led_indicator_on_led_state_changed</a> from uart_led_indicator.o(i.uart_led_indicator_on_led_state_changed) referenced from uart_led_indicator.o(i._start_led_indication)
 <LI><a href="#[8]">uart_recv</a> from uart_dev.o(i.uart_recv) referenced 2 times from uart_dev.o(.data)
 <LI><a href="#[7]">uart_send</a> from uart_dev.o(i.uart_send) referenced 2 times from uart_dev.o(.data)
 <LI><a href="#[2d]">vPortFree</a> from heap_4.o(i.vPortFree) referenced 8 times from system_adaption.o(.data)
 <LI><a href="#[2d]">vPortFree</a> from heap_4.o(i.vPortFree) referenced from system_adaption.o(i.led_handler_inst)
 <LI><a href="#[16]">write_modbus_data</a> from system_adaption.o(i.write_modbus_data) referenced 2 times from system_adaption.o(.data)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[93]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(.text)
</UL>
<P><STRONG><a name="[2db]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[ef]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[106]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[2dc]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[2dd]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[2de]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[2df]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[2e0]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[54]"></a>SVC_Handler</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[2d3]"></a>__asm___6_port_c_39a90d8d__prvStartFirstTask</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[2d2]"></a>__asm___6_port_c_39a90d8d__prvEnableVFP</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[56]"></a>PendSV_Handler</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = PendSV_Handler &rArr; vTaskSwitchContext
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[2c4]"></a>vPortGetIPSR</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
</UL>

<P><STRONG><a name="[2e1]"></a>__asm___14_cm_backtrace_c_13c737ea__cmb_get_msp</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, cm_backtrace.o(.emb_text), UNUSED)

<P><STRONG><a name="[21a]"></a>__asm___14_cm_backtrace_c_13c737ea__cmb_get_psp</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, cm_backtrace.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_call_stack
</UL>

<P><STRONG><a name="[219]"></a>__asm___14_cm_backtrace_c_13c737ea__cmb_get_sp</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, cm_backtrace.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_call_stack
</UL>

<P><STRONG><a name="[4e]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[7e]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[82]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[83]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[84]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[85]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[86]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[88]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[89]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[8a]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[7b]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[8e]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[76]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[8d]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[8c]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[87]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[7f]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[77]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[81]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[8f]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[90]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[80]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[79]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[8b]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f411xe.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>HardFault_Handler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, cmb_fault.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = HardFault_Handler &rArr; cm_backtrace_fault &rArr; print_call_stack &rArr; cm_backtrace_call_stack &rArr; get_cur_thread_stack_info &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[f3]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[2e2]"></a>___aeabi_memcpy8$move</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[1c6]"></a>__aeabi_memcpy</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, memmovea.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_buff_write
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_buff_read
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataFromQueue
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteNoCheck
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteBlocking
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;async_put_log
</UL>

<P><STRONG><a name="[10a]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_start_led_indication
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_handler_control
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_led_handler_control_internal
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_collect_event_thread
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_voltage_data
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_handler_inst
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_median_average
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_selection_sort_f32
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_quick_sort_f32
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_insertion_sort_f32
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_heap_sort_f32
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_bubble_sort_f32
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_bitonic_sort_f32
</UL>

<P><STRONG><a name="[2e3]"></a>__aeabi_memmove</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[1e6]"></a>__aeabi_memmove4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memmove4
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_collection_data_thread
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__process_amp_result_data
</UL>

<P><STRONG><a name="[2e4]"></a>__aeabi_memmove8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, memmovea.o(.text), UNUSED)

<P><STRONG><a name="[f7]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memset
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[2e5]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[2e6]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[f6]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_buff_init
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_set_filter_tag_lvl_default
</UL>

<P><STRONG><a name="[108]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_collect_event_thread
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_MspInit
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_fir_init_f32
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_biquad_cascade_df1_init_f32
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_event_handler_thread
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_proc_handler_thread
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_DoInit
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_call_stack
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_collection_data_thread
</UL>

<P><STRONG><a name="[2e7]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[f8]"></a>memset</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[23b]"></a>strstr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>

<P><STRONG><a name="[223]"></a>strncpy</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, strncpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_init
</UL>

<P><STRONG><a name="[23a]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>

<P><STRONG><a name="[1aa]"></a>strcmp</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, strcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_uart_dev
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_rs485_dev
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rs485DeviceInit
</UL>

<P><STRONG><a name="[1d0]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__flash_check_value
</UL>

<P><STRONG><a name="[1cf]"></a>strncmp</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__flash_check_value
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_get_filter_tag_lvl
</UL>

<P><STRONG><a name="[f9]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_fence_compensation
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[fd]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[fe]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hamming
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[ff]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hanning
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hamming
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__process_amp_result_data
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[100]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__process_amp_result_data
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[101]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[16a]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_cmd_0x50_Reply
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_collection_data_thread
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_fence_compensation
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hanning
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hamming
<LI><a href="#[256]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fft_calculate
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__process_amp_result_data
</UL>

<P><STRONG><a name="[102]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_fence_compensation
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hanning
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hamming
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__process_amp_result_data
</UL>

<P><STRONG><a name="[2e8]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[1f2]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[f5]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[2e9]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[f4]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[2ea]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[fa]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[2eb]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[2ec]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[103]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[2ed]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[fc]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[fb]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>

<P><STRONG><a name="[1e1]"></a>__ARM_scalbn</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, dscalb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[2ee]"></a>scalbn</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, dscalb.o(.text), UNUSED)

<P><STRONG><a name="[104]"></a>_dsqrt</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, dsqrt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[105]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[1e0]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[f0]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[2ef]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[2f0]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[2f1]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[270]"></a>arm_biquad_cascade_df1_f32</STRONG> (Thumb, 388 bytes, Stack size 24 bytes, arm_biquad_cascade_df1_f32.o(.text.arm_biquad_cascade_df1_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = arm_biquad_cascade_df1_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lpf_iir
</UL>

<P><STRONG><a name="[107]"></a>arm_biquad_cascade_df1_init_f32</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, arm_biquad_cascade_df1_init_f32.o(.text.arm_biquad_cascade_df1_init_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = arm_biquad_cascade_df1_init_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lpf_fir_init
</UL>

<P><STRONG><a name="[109]"></a>arm_bitonic_sort_f32</STRONG> (Thumb, 252 bytes, Stack size 56 bytes, arm_bitonic_sort_f32.o(.text.arm_bitonic_sort_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = arm_bitonic_sort_f32 &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sort_f32
</UL>

<P><STRONG><a name="[110]"></a>arm_bitreversal_32</STRONG> (Thumb, 68 bytes, Stack size 20 bytes, arm_bitreversal2.o(.text.arm_bitreversal_32))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = arm_bitreversal_32
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_f32
</UL>

<P><STRONG><a name="[10b]"></a>arm_bubble_sort_f32</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, arm_bubble_sort_f32.o(.text.arm_bubble_sort_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = arm_bubble_sort_f32 &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sort_f32
</UL>

<P><STRONG><a name="[10c]"></a>arm_cfft_f32</STRONG> (Thumb, 224 bytes, Stack size 24 bytes, arm_cfft_f32.o(.text.arm_cfft_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix8_butterfly_f32
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix8by4_f32
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix8by2_f32
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_bitreversal_32
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
</UL>

<P><STRONG><a name="[10e]"></a>arm_cfft_radix8by2_f32</STRONG> (Thumb, 408 bytes, Stack size 40 bytes, arm_cfft_f32.o(.text.arm_cfft_radix8by2_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = arm_cfft_radix8by2_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix8_butterfly_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_f32
</UL>

<P><STRONG><a name="[10d]"></a>arm_cfft_radix8by4_f32</STRONG> (Thumb, 1020 bytes, Stack size 128 bytes, arm_cfft_f32.o(.text.arm_cfft_radix8by4_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix8_butterfly_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_f32
</UL>

<P><STRONG><a name="[111]"></a>arm_cmplx_mag_f32</STRONG> (Thumb, 340 bytes, Stack size 40 bytes, arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = arm_cmplx_mag_f32 &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
</UL>

<P><STRONG><a name="[12a]"></a>arm_cos_f32</STRONG> (Thumb, 152 bytes, Stack size 0 bytes, arm_cos_f32.o(.text.arm_cos_f32))
<BR><BR>[Called By]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hanning
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hamming
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlattopWin
</UL>

<P><STRONG><a name="[26f]"></a>arm_fir_f32</STRONG> (Thumb, 1196 bytes, Stack size 72 bytes, arm_fir_f32.o(.text.arm_fir_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = arm_fir_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lpf_fir
</UL>

<P><STRONG><a name="[113]"></a>arm_fir_init_f32</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, arm_fir_init_f32.o(.text.arm_fir_init_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = arm_fir_init_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lpf_fir_init
</UL>

<P><STRONG><a name="[114]"></a>arm_heap_sort_f32</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, arm_heap_sort_f32.o(.text.arm_heap_sort_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = arm_heap_sort_f32 &rArr; arm_heapify
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_heapify
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sort_f32
</UL>

<P><STRONG><a name="[116]"></a>arm_insertion_sort_f32</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, arm_insertion_sort_f32.o(.text.arm_insertion_sort_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = arm_insertion_sort_f32 &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sort_f32
</UL>

<P><STRONG><a name="[118]"></a>arm_quick_sort_f32</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, arm_quick_sort_f32.o(.text.arm_quick_sort_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = arm_quick_sort_f32 &rArr; arm_quick_sort_core_f32 &rArr;  arm_quick_sort_core_f32 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_quick_sort_core_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sort_f32
</UL>

<P><STRONG><a name="[10f]"></a>arm_radix8_butterfly_f32</STRONG> (Thumb, 1458 bytes, Stack size 232 bytes, arm_cfft_radix8_f32.o(.text.arm_radix8_butterfly_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = arm_radix8_butterfly_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_f32
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix8by4_f32
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix8by2_f32
</UL>

<P><STRONG><a name="[119]"></a>arm_selection_sort_f32</STRONG> (Thumb, 134 bytes, Stack size 24 bytes, arm_selection_sort_f32.o(.text.arm_selection_sort_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = arm_selection_sort_f32 &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sort_f32
</UL>

<P><STRONG><a name="[11a]"></a>arm_sort_f32</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, arm_sort_f32.o(.text.arm_sort_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = arm_sort_f32 &rArr; arm_bitonic_sort_f32 &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_selection_sort_f32
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_quick_sort_f32
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_insertion_sort_f32
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_heap_sort_f32
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_bubble_sort_f32
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_bitonic_sort_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MidFilterBlock
</UL>

<P><STRONG><a name="[11c]"></a>ADDLinked_List</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, linked_list.o(i.ADDLinked_List))
<BR><BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_handler_register_driver
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_ad7606_driver_instance
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADDUARTDEVICE
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADDRS485DEVICE
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dev_instance
</UL>

<P><STRONG><a name="[11b]"></a>ADDRS485DEVICE</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, rs485_dev.o(i.ADDRS485DEVICE))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADDRS485DEVICE
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADDLinked_List
</UL>
<BR>[Called By]<UL><LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handler_init
</UL>

<P><STRONG><a name="[11d]"></a>ADDUARTDEVICE</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, uart_dev.o(i.ADDUARTDEVICE))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADDUARTDEVICE
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADDLinked_List
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rs485DeviceInit
</UL>

<P><STRONG><a name="[52]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[213]"></a>DELLinked_List</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, linked_list.o(i.DELLinked_List))
<BR><BR>[Called By]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_handler_unregister_driver
</UL>

<P><STRONG><a name="[66]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.DMA1_Stream3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Stream3_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.DMA1_Stream4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Stream4_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Stream5_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.DMA1_Stream6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Stream6_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.EXTI4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EXTI4_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Callback
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[150]"></a>Error_Handler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CRC_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM10_Init
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[120]"></a>FLASH_Erase_Sector</STRONG> (Thumb, 160 bytes, Stack size 16 bytes, stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FLASH_Erase_Sector
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_Erase
</UL>

<P><STRONG><a name="[135]"></a>FLASH_FlushCaches</STRONG> (Thumb, 114 bytes, Stack size 0 bytes, stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches))
<BR><BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_Erase
</UL>

<P><STRONG><a name="[127]"></a>FLASH_WaitForLastOperation</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_SetErrorCode
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_Erase
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Program
</UL>

<P><STRONG><a name="[129]"></a>FlattopWin</STRONG> (Thumb, 266 bytes, Stack size 48 bytes, fft_calculate_task.o(i.FlattopWin))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = FlattopWin
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cos_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
</UL>

<P><STRONG><a name="[1e8]"></a>GetNextNode</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, linked_list.o(i.GetNextNode))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_handler_task
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_find_led_state_by_which
</UL>

<P><STRONG><a name="[12b]"></a>HAL_CRC_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, stm32f4xx_hal_crc.o(i.HAL_CRC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_CRC_Init &rArr; HAL_CRC_MspInit
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CRC_MspInit
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CRC_Init
</UL>

<P><STRONG><a name="[12c]"></a>HAL_CRC_MspInit</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, crc.o(i.HAL_CRC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_CRC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CRC_Init
</UL>

<P><STRONG><a name="[12d]"></a>HAL_DMA_Abort</STRONG> (Thumb, 172 bytes, Stack size 16 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[158]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
</UL>

<P><STRONG><a name="[12e]"></a>HAL_DMA_DeInit</STRONG> (Thumb, 280 bytes, Stack size 16 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_DMA_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcBaseAndBitshift
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspDeInit
</UL>

<P><STRONG><a name="[11e]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 570 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream6_IRQHandler
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream5_IRQHandler
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream4_IRQHandler
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream3_IRQHandler
</UL>

<P><STRONG><a name="[130]"></a>HAL_DMA_Init</STRONG> (Thumb, 792 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CheckFifoParam
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcBaseAndBitshift
</UL>
<BR>[Called By]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>

<P><STRONG><a name="[132]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 166 bytes, Stack size 32 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit_DMA
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive_DMA
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive_DMA
</UL>

<P><STRONG><a name="[134]"></a>HAL_FLASHEx_Erase</STRONG> (Thumb, 210 bytes, Stack size 24 bytes, stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_FLASHEx_Erase &rArr; FLASH_MassErase
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_MassErase
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_FlushCaches
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Erase_Sector
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;internal_flash_write
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;internal_flash_erase
</UL>

<P><STRONG><a name="[268]"></a>HAL_FLASH_Lock</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock))
<BR><BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;internal_flash_write
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;internal_flash_erase
</UL>

<P><STRONG><a name="[136]"></a>HAL_FLASH_Program</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, stm32f4xx_hal_flash.o(i.HAL_FLASH_Program))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_FLASH_Program &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Program_Word
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Program_HalfWord
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Program_DoubleWord
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Program_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;internal_flash_write
</UL>

<P><STRONG><a name="[266]"></a>HAL_FLASH_Unlock</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock))
<BR><BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;internal_flash_write
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;internal_flash_erase
</UL>

<P><STRONG><a name="[e]"></a>HAL_GPIO_DeInit</STRONG> (Thumb, 320 bytes, Stack size 32 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspDeInit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[137]"></a>HAL_GPIO_EXTI_Callback</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, system_adaption.o(i.HAL_GPIO_EXTI_Callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_GPIO_EXTI_Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>

<P><STRONG><a name="[11f]"></a>HAL_GPIO_EXTI_IRQHandler</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Callback
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI4_IRQHandler
</UL>

<P><STRONG><a name="[138]"></a>HAL_GPIO_Init</STRONG> (Thumb, 850 bytes, Stack size 32 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_MspInit
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[10]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_GPIO_ReadPin
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[11]"></a>HAL_GPIO_TogglePin</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_GPIO_TogglePin
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[f]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_GPIO_WritePin
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_spi_cs_low
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcu_spi_cs_high
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[25]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitReceiveCplt
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitCplt
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAReceiveCplt
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseTx_ISR
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRx_ISR
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_get_time
</UL>
<BR>[Address Reference Count : 2]<UL><LI> system_adaption.o(i.led_handler_inst)
<LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[179]"></a>HAL_IncTick</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
</UL>

<P><STRONG><a name="[139]"></a>HAL_Init</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, stm32f4xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13b]"></a>HAL_InitTick</STRONG> (Thumb, 162 bytes, Stack size 56 bytes, stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[13c]"></a>HAL_MspInit</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, stm32f4xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[143]"></a>HAL_NVIC_DisableIRQ</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_NVIC_DisableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspDeInit
</UL>

<P><STRONG><a name="[141]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_MspInit
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_collect_xxxx_interrupt_enable
</UL>

<P><STRONG><a name="[142]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 152 bytes, Stack size 40 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_GetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_MspInit
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_collect_xxxx_interrupt_init
</UL>

<P><STRONG><a name="[13a]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[146]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 784 bytes, Stack size 24 bytes, stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
</UL>

<P><STRONG><a name="[147]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 628 bytes, Stack size 16 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[13d]"></a>HAL_RCC_GetClockConfig</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig))
<BR><BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[149]"></a>HAL_RCC_GetHCLKFreq</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq))
<BR><BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
</UL>

<P><STRONG><a name="[13e]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[14a]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK2Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[148]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[14b]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1556 bytes, Stack size 24 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[14c]"></a>HAL_RTC_Init</STRONG> (Thumb, 342 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(i.HAL_RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_RTC_Init &rArr; HAL_RTC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ExitInitMode
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
</UL>
<BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
</UL>

<P><STRONG><a name="[14d]"></a>HAL_RTC_MspInit</STRONG> (Thumb, 52 bytes, Stack size 32 bytes, rtc.o(i.HAL_RTC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_RTC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[151]"></a>HAL_RTC_SetDate</STRONG> (Thumb, 406 bytes, Stack size 24 bytes, stm32f4xx_hal_rtc.o(i.HAL_RTC_SetDate))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_RTC_SetDate &rArr; RTC_ExitInitMode &rArr; HAL_RTC_WaitForSynchro
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ExitInitMode
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ByteToBcd2
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Bcd2ToByte
</UL>
<BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
</UL>

<P><STRONG><a name="[154]"></a>HAL_RTC_SetTime</STRONG> (Thumb, 522 bytes, Stack size 24 bytes, stm32f4xx_hal_rtc.o(i.HAL_RTC_SetTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_RTC_SetTime &rArr; RTC_ExitInitMode &rArr; HAL_RTC_WaitForSynchro
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ExitInitMode
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ByteToBcd2
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Bcd2ToByte
</UL>
<BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
</UL>

<P><STRONG><a name="[155]"></a>HAL_RTC_WaitForSynchro</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_RTC_WaitForSynchro
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ExitInitMode
</UL>

<P><STRONG><a name="[31]"></a>HAL_SPI_DeInit</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_SPI_DeInit &rArr; HAL_SPI_MspDeInit &rArr; HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspDeInit
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[159]"></a>HAL_SPI_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitReceiveCplt
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitCplt
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAReceiveCplt
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAError
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAAbortOnError
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseTx_ISR
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRx_ISR
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
</UL>

<P><STRONG><a name="[157]"></a>HAL_SPI_IRQHandler</STRONG> (Thumb, 342 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_SPI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI2_IRQHandler
</UL>

<P><STRONG><a name="[15a]"></a>HAL_SPI_Init</STRONG> (Thumb, 628 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
</UL>

<P><STRONG><a name="[156]"></a>HAL_SPI_MspDeInit</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, spi.o(i.HAL_SPI_MspDeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_SPI_MspDeInit &rArr; HAL_GPIO_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_DisableIRQ
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_DeInit
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_DeInit
</UL>

<P><STRONG><a name="[15b]"></a>HAL_SPI_MspInit</STRONG> (Thumb, 324 bytes, Stack size 32 bytes, spi.o(i.HAL_SPI_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_SPI_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>

<P><STRONG><a name="[33]"></a>HAL_SPI_Receive</STRONG> (Thumb, 386 bytes, Stack size 32 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = HAL_SPI_Receive &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTransaction
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[37]"></a>HAL_SPI_Receive_DMA</STRONG> (Thumb, 300 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = HAL_SPI_Receive_DMA &rArr; HAL_SPI_TransmitReceive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive_DMA
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[35]"></a>HAL_SPI_Receive_IT</STRONG> (Thumb, 202 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_SPI_Receive_IT &rArr; HAL_SPI_TransmitReceive_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive_IT
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[160]"></a>HAL_SPI_RxCpltCallback</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, system_adaption.o(i.HAL_SPI_RxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = HAL_SPI_RxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAReceiveCplt
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRx_ISR
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
</UL>

<P><STRONG><a name="[1ba]"></a>HAL_SPI_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAHalfReceiveCplt
</UL>

<P><STRONG><a name="[32]"></a>HAL_SPI_Transmit</STRONG> (Thumb, 472 bytes, Stack size 32 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_SPI_Transmit &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[15c]"></a>HAL_SPI_TransmitReceive</STRONG> (Thumb, 602 bytes, Stack size 48 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
</UL>

<P><STRONG><a name="[15e]"></a>HAL_SPI_TransmitReceive_DMA</STRONG> (Thumb, 384 bytes, Stack size 32 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = HAL_SPI_TransmitReceive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive_DMA
</UL>

<P><STRONG><a name="[15f]"></a>HAL_SPI_TransmitReceive_IT</STRONG> (Thumb, 198 bytes, Stack size 32 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_SPI_TransmitReceive_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive_IT
</UL>

<P><STRONG><a name="[36]"></a>HAL_SPI_Transmit_DMA</STRONG> (Thumb, 278 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_SPI_Transmit_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[34]"></a>HAL_SPI_Transmit_IT</STRONG> (Thumb, 194 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_SPI_Transmit_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[163]"></a>HAL_SPI_TxCpltCallback</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, system_adaption.o(i.HAL_SPI_TxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = HAL_SPI_TxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitCplt
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseTx_ISR
</UL>

<P><STRONG><a name="[1bb]"></a>HAL_SPI_TxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAHalfTransmitCplt
</UL>

<P><STRONG><a name="[1b7]"></a>HAL_SPI_TxRxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitReceiveCplt
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
</UL>

<P><STRONG><a name="[1bc]"></a>HAL_SPI_TxRxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAHalfTransmitReceiveCplt
</UL>

<P><STRONG><a name="[176]"></a>HAL_TIMEx_BreakCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback))
<BR><BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[178]"></a>HAL_TIMEx_CommutCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback))
<BR><BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[164]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 270 bytes, Stack size 24 bytes, stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[13f]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 340 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM10_Init
</UL>

<P><STRONG><a name="[165]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, tim.o(i.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[140]"></a>HAL_TIM_Base_Start_IT</STRONG> (Thumb, 200 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIM_Base_Start_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_start_collect_data
</UL>

<P><STRONG><a name="[167]"></a>HAL_TIM_Base_Stop_IT</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_TIM_Base_Stop_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_stop_collect_data
</UL>

<P><STRONG><a name="[168]"></a>HAL_TIM_IC_CaptureCallback</STRONG> (Thumb, 406 bytes, Stack size 24 bytes, system_adaption.o(i.HAL_TIM_IC_CaptureCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 432<LI>Call Chain = HAL_TIM_IC_CaptureCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_printf
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ReadCapturedValue
</UL>
<BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[16b]"></a>HAL_TIM_IC_ConfigChannel</STRONG> (Thumb, 572 bytes, Stack size 24 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_TIM_IC_ConfigChannel &rArr; TIM_TI4_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI4_SetConfig
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI3_SetConfig
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI2_SetConfig
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI1_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[170]"></a>HAL_TIM_IC_Init</STRONG> (Thumb, 340 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_TIM_IC_Init &rArr; HAL_TIM_IC_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_MspInit
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[171]"></a>HAL_TIM_IC_MspInit</STRONG> (Thumb, 256 bytes, Stack size 32 bytes, tim.o(i.HAL_TIM_IC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_TIM_IC_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_Init
</UL>

<P><STRONG><a name="[172]"></a>HAL_TIM_IRQHandler</STRONG> (Thumb, 364 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = HAL_TIM_IRQHandler &rArr; HAL_TIM_IC_CaptureCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_TriggerCallback
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_PulseFinishedCallback
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_OC_DelayElapsedCallback
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_CommutCallback
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_BreakCallback
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_IRQHandler
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_IRQHandler
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_IRQHandler
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_UP_TIM10_IRQHandler
</UL>

<P><STRONG><a name="[173]"></a>HAL_TIM_OC_DelayElapsedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[174]"></a>HAL_TIM_PWM_PulseFinishedCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback))
<BR><BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[175]"></a>HAL_TIM_PeriodElapsedCallback</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, main.o(i.HAL_TIM_PeriodElapsedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_TIM_PeriodElapsedCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[169]"></a>HAL_TIM_ReadCapturedValue</STRONG> (Thumb, 298 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIM_ReadCapturedValue
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
</UL>

<P><STRONG><a name="[17a]"></a>HAL_TIM_SlaveConfigSynchro</STRONG> (Thumb, 266 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = HAL_TIM_SlaveConfigSynchro &rArr; TIM_SlaveTimer_SetConfig &rArr; TIM_TI2_ConfigInputStage
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SlaveTimer_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[177]"></a>HAL_TIM_TriggerCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback))
<BR><BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[e7]"></a>HAL_UARTEx_ReceiveToIdle_IT</STRONG> (Thumb, 114 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_UARTEx_ReceiveToIdle_IT &rArr; UART_Start_Receive_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_IT
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(i.uart_dev_register)
</UL>
<P><STRONG><a name="[17d]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, uart_dev.o(i.HAL_UARTEx_RxEventCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = HAL_UARTEx_RxEventCallback &rArr; uart_led_indicator_on_data_received &rArr; _start_led_indication &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_led_indicator_on_data_received
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
</UL>

<P><STRONG><a name="[180]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, uart_dev.o(i.HAL_UART_ErrorCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_UART_ErrorCallback &rArr; HAL_UART_Receive_IT &rArr; UART_Start_Receive_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
</UL>

<P><STRONG><a name="[181]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 772 bytes, Stack size 40 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; uart_led_indicator_on_data_received &rArr; _start_led_indication &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Transmit_IT
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[186]"></a>HAL_UART_Init</STRONG> (Thumb, 278 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_UART_Init &rArr; UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
</UL>

<P><STRONG><a name="[187]"></a>HAL_UART_MspInit</STRONG> (Thumb, 258 bytes, Stack size 32 bytes, usart.o(i.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[e9]"></a>HAL_UART_Receive</STRONG> (Thumb, 194 bytes, Stack size 40 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_UART_Receive &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(i.uart_dev_register)
</UL>
<P><STRONG><a name="[e8]"></a>HAL_UART_Receive_IT</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_UART_Receive_IT &rArr; UART_Start_Receive_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(i.uart_dev_register)
</UL>
<P><STRONG><a name="[18a]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, uart_dev.o(i.HAL_UART_RxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = HAL_UART_RxCpltCallback &rArr; uart_led_indicator_on_data_received &rArr; _start_led_indication &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_led_indicator_on_data_received
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
</UL>
<BR>[Called By]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
</UL>

<P><STRONG><a name="[eb]"></a>HAL_UART_Transmit</STRONG> (Thumb, 190 bytes, Stack size 40 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(i.uart_dev_register)
</UL>
<P><STRONG><a name="[ea]"></a>HAL_UART_Transmit_IT</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_UART_Transmit_IT
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(i.uart_dev_register)
</UL>
<P><STRONG><a name="[18b]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, uart_dev.o(i.HAL_UART_TxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_UART_TxCpltCallback &rArr; xQueueGiveFromISR &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGiveFromISR
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
</UL>

<P><STRONG><a name="[18d]"></a>Hamming</STRONG> (Thumb, 130 bytes, Stack size 56 bytes, fft_calculate_task.o(i.Hamming))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = Hamming &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cos_f32
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
</UL>

<P><STRONG><a name="[18e]"></a>Hanning</STRONG> (Thumb, 122 bytes, Stack size 48 bytes, fft_calculate_task.o(i.Hanning))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = Hanning &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cos_f32
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
</UL>

<P><STRONG><a name="[18f]"></a>MX_CRC_Init</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, crc.o(i.MX_CRC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = MX_CRC_Init &rArr; HAL_CRC_Init &rArr; HAL_CRC_MspInit
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CRC_Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[190]"></a>MX_DMA_Init</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, dma.o(i.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[191]"></a>MX_FREERTOS_Init</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, freertos.o(i.MX_FREERTOS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 472<LI>Call Chain = MX_FREERTOS_Init &rArr; system_source_inst &rArr; led_handler_inst &rArr; led_driver_inst &rArr; bsp_led_driver_inst &rArr; bsp_led_driver_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_source_inst
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osThreadNew
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_elog
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d]"></a>MX_GPIO_Init</STRONG> (Thumb, 338 bytes, Stack size 32 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = MX_GPIO_Init &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[196]"></a>MX_RTC_Init</STRONG> (Thumb, 128 bytes, Stack size 32 bytes, rtc.o(i.MX_RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = MX_RTC_Init &rArr; HAL_RTC_Init &rArr; HAL_RTC_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[197]"></a>MX_SPI2_Init</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, spi.o(i.MX_SPI2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_SPI2_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dev_init
</UL>

<P><STRONG><a name="[198]"></a>MX_TIM10_Init</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, tim.o(i.MX_TIM10_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = MX_TIM10_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[199]"></a>MX_TIM3_Init</STRONG> (Thumb, 158 bytes, Stack size 48 bytes, tim.o(i.MX_TIM3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = MX_TIM3_Init &rArr; HAL_TIM_IC_Init &rArr; HAL_TIM_IC_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_SlaveConfigSynchro
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_Init
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_ConfigChannel
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[19a]"></a>MX_TIM4_Init</STRONG> (Thumb, 158 bytes, Stack size 48 bytes, tim.o(i.MX_TIM4_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = MX_TIM4_Init &rArr; HAL_TIM_IC_Init &rArr; HAL_TIM_IC_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_SlaveConfigSynchro
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_Init
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_ConfigChannel
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ec]"></a>MX_USART2_UART_Init</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, usart.o(i.MX_USART2_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = MX_USART2_UART_Init &rArr; HAL_UART_Init &rArr; UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(i.uart_dev_register)
</UL>
<P><STRONG><a name="[51]"></a>MemManage_Handler</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = MemManage_Handler &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[19c]"></a>MidFilterBlock</STRONG> (Thumb, 154 bytes, Stack size 32 bytes, data_proc_calcu_fun.o(i.MidFilterBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = MidFilterBlock &rArr; arm_sort_f32 &rArr; arm_bitonic_sort_f32 &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sort_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_median_average
</UL>

<P><STRONG><a name="[19d]"></a>Modbus_RTU_cmd_Recv</STRONG> (Thumb, 990 bytes, Stack size 16 bytes, modbus_rtu.o(i.Modbus_RTU_cmd_Recv))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Modbus_RTU_cmd_Recv &rArr; timer_start
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_start
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_rtu_crc16
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_rtu_check_fun_code
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_output_msg_thread
</UL>

<P><STRONG><a name="[1a1]"></a>Modbus_rtu_Reply</STRONG> (Thumb, 168 bytes, Stack size 8 bytes, modbus_rtu.o(i.Modbus_rtu_Reply))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = Modbus_rtu_Reply &rArr; Modbus_rtu_cmd_0x50_Reply &rArr; __merge_reg_data &rArr; modbus_read_reg &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_wait_errordata_recv
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_cmd_0x50_Reply
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_cmd_0x10_Reply
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_cmd_0x06_Reply
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_cmd_0x03_Reply
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_output_msg_thread
</UL>

<P><STRONG><a name="[1a3]"></a>Modbus_rtu_cmd_0x03_Reply</STRONG> (Thumb, 182 bytes, Stack size 16 bytes, modbus_rtu.o(i.Modbus_rtu_cmd_0x03_Reply))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = Modbus_rtu_cmd_0x03_Reply &rArr; modbus_read_reg &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_read_reg
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_rtu_crc16
</UL>
<BR>[Called By]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_Reply
</UL>

<P><STRONG><a name="[1a4]"></a>Modbus_rtu_cmd_0x06_Reply</STRONG> (Thumb, 218 bytes, Stack size 16 bytes, modbus_rtu.o(i.Modbus_rtu_cmd_0x06_Reply))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = Modbus_rtu_cmd_0x06_Reply &rArr; modbus_write_reg &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_write_reg
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_rtu_crc16
</UL>
<BR>[Called By]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_Reply
</UL>

<P><STRONG><a name="[1a5]"></a>Modbus_rtu_cmd_0x10_Reply</STRONG> (Thumb, 220 bytes, Stack size 16 bytes, modbus_rtu.o(i.Modbus_rtu_cmd_0x10_Reply))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = Modbus_rtu_cmd_0x10_Reply &rArr; modbus_write_reg &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_write_reg
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_rtu_crc16
</UL>
<BR>[Called By]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_Reply
</UL>

<P><STRONG><a name="[1a6]"></a>Modbus_rtu_cmd_0x50_Reply</STRONG> (Thumb, 828 bytes, Stack size 40 bytes, modbus_rtu.o(i.Modbus_rtu_cmd_0x50_Reply))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = Modbus_rtu_cmd_0x50_Reply &rArr; __merge_reg_data &rArr; modbus_read_reg &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__merge_reg_data
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_Reply
</UL>

<P><STRONG><a name="[4f]"></a>NMI_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[153]"></a>RTC_Bcd2ToByte</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc.o(i.RTC_Bcd2ToByte))
<BR><BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
</UL>

<P><STRONG><a name="[152]"></a>RTC_ByteToBcd2</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal_rtc.o(i.RTC_ByteToBcd2))
<BR><BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
</UL>

<P><STRONG><a name="[14e]"></a>RTC_EnterInitMode</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RTC_EnterInitMode
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[14f]"></a>RTC_ExitInitMode</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(i.RTC_ExitInitMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = RTC_ExitInitMode &rArr; HAL_RTC_WaitForSynchro
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[1ab]"></a>SEGGER_RTT_Write</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, SEGGER_RTT.o(i.SEGGER_RTT_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_DoInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_vprintf
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_StoreChar
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_output
</UL>

<P><STRONG><a name="[1ad]"></a>SEGGER_RTT_WriteNoLock</STRONG> (Thumb, 126 bytes, Stack size 32 bytes, SEGGER_RTT.o(i.SEGGER_RTT_WriteNoLock))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteNoCheck
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteBlocking
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_GetAvailWriteSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_Write
</UL>

<P><STRONG><a name="[1b1]"></a>SEGGER_RTT_printf</STRONG> (Thumb, 34 bytes, Stack size 32 bytes, SEGGER_RTT_printf.o(i.SEGGER_RTT_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_vprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_printf
</UL>

<P><STRONG><a name="[1b2]"></a>SEGGER_RTT_vprintf</STRONG> (Thumb, 522 bytes, Stack size 136 bytes, SEGGER_RTT_printf.o(i.SEGGER_RTT_vprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_StoreChar
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_PrintUnsigned
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_PrintInt
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_printf
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_printf
</UL>

<P><STRONG><a name="[78]"></a>SPI2_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.SPI2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SPI2_IRQHandler &rArr; HAL_SPI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[a5]"></a>StartDefaultTask</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, freertos.o(i.StartDefaultTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = StartDefaultTask &rArr; osDelay &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(i.MX_FREERTOS_Init)
</UL>
<P><STRONG><a name="[57]"></a>SysTick_Handler</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, cmsis_os2.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SysTick_Handler &rArr; xPortSysTickHandler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[1c2]"></a>SystemClock_Config</STRONG> (Thumb, 174 bytes, Stack size 80 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[92]"></a>SystemInit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, system_stm32f4xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(.text)
</UL>
<P><STRONG><a name="[6d]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.TIM1_UP_TIM10_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = TIM1_UP_TIM10_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_IC_CaptureCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>TIM2_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.TIM2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = TIM2_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_IC_CaptureCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>TIM3_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.TIM3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = TIM3_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_IC_CaptureCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>TIM4_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.TIM4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = TIM4_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_IC_CaptureCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[166]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 142 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_Init
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[1c3]"></a>TIM_ETR_SetConfig</STRONG> (Thumb, 22 bytes, Stack size 12 bytes, stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_ETR_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SlaveTimer_SetConfig
</UL>

<P><STRONG><a name="[16c]"></a>TIM_TI1_SetConfig</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_TI1_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_ConfigChannel
</UL>

<P><STRONG><a name="[17c]"></a>UART_Start_Receive_IT</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_Start_Receive_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_IT
</UL>

<P><STRONG><a name="[7a]"></a>USART2_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = USART2_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; uart_led_indicator_on_data_received &rArr; _start_led_indication &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f411xe.o(RESET)
</UL>
<P><STRONG><a name="[253]"></a>Window</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, fft_calculate_task.o(i.Window))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Window
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
</UL>

<P><STRONG><a name="[1c7]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2f2]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[19b]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_cmd_0x50_Reply
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_init
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_firmware_info
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_call_stack
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_cur_thread_stack_info
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fault_diagnosis
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dump_stack
</UL>

<P><STRONG><a name="[2f3]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[2f4]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[1c9]"></a>__0snprintf</STRONG> (Thumb, 44 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2f5]"></a>__1snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[243]"></a>__2snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>

<P><STRONG><a name="[2f6]"></a>__c89snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[2f7]"></a>snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[1ca]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2f8]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[28a]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_call_stack
</UL>

<P><STRONG><a name="[2f9]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[2fa]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[1cb]"></a>__0vsnprintf</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2fb]"></a>__1vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[2fc]"></a>__2vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[2fd]"></a>__c89vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[244]"></a>vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>

<P><STRONG><a name="[1e2]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1d3]"></a>__ARM_fpclassifyf</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fpclassifyf.o(i.__ARM_fpclassifyf))
<BR><BR>[Called By]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[22a]"></a>__data_proc_mount_handler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, data_process_task.o(i.__data_proc_mount_handler))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_proc_handler_thread
</UL>

<P><STRONG><a name="[1cc]"></a>__fft_calcu_mount_handler</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, fft_calculate_task.o(i.__fft_calcu_mount_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = __fft_calcu_mount_handler &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_event_handler_thread
</UL>

<P><STRONG><a name="[2bf]"></a>__find_spi_dev_private_data</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, mcu_spi_driver.o(i.__find_spi_dev_private_data))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_interrupt_txcallbackfun
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_interrupt_rxcallbackfun
</UL>

<P><STRONG><a name="[1d2]"></a>__hardfp_atan2f</STRONG> (Thumb, 502 bytes, Stack size 16 bytes, atan2f.o(i.__hardfp_atan2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __hardfp_atan2f
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan2
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_phase_radian
</UL>

<P><STRONG><a name="[1d6]"></a>__hardfp_pow</STRONG> (Thumb, 3072 bytes, Stack size 192 bytes, pow.o(i.__hardfp_pow))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
</UL>
<BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_fence_compensation
</UL>

<P><STRONG><a name="[1e3]"></a>__hardfp_sqrt</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, sqrt.o(i.__hardfp_sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __hardfp_sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_fence_compensation
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__process_amp_result_data
</UL>

<P><STRONG><a name="[112]"></a>__hardfp_sqrtf</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, sqrtf.o(i.__hardfp_sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cmplx_mag_f32
</UL>

<P><STRONG><a name="[1df]"></a>__kernel_poly</STRONG> (Thumb, 248 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __kernel_poly &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1d9]"></a>__mathlib_dbl_divzero</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_divzero))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __mathlib_dbl_divzero &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1d7]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_infnan2 &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1dd]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1da]"></a>__mathlib_dbl_overflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_overflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_overflow &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1de]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_underflow &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1d5]"></a>__mathlib_flt_infnan2</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan2))
<BR><BR>[Called By]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[1d4]"></a>__mathlib_flt_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_underflow))
<BR><BR>[Called By]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[1a9]"></a>__merge_reg_data</STRONG> (Thumb, 78 bytes, Stack size 40 bytes, modbus_rtu.o(i.__merge_reg_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = __merge_reg_data &rArr; modbus_read_reg &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_read_reg
</UL>
<BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_cmd_0x50_Reply
</UL>

<P><STRONG><a name="[1e4]"></a>__process_amp_result_data</STRONG> (Thumb, 300 bytes, Stack size 56 bytes, data_proc_calcu_fun.o(i.__process_amp_result_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = __process_amp_result_data &rArr; calculate_median_average &rArr; MidFilterBlock &rArr; arm_sort_f32 &rArr; arm_bitonic_sort_f32 &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_median_average
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memmove4
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_voltage_data
</UL>

<P><STRONG><a name="[2fe]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[2ff]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[300]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[1d8]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1fc]"></a>ad7606_spi_dev_source_inst</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, system_adaption.o(i.ad7606_spi_dev_source_inst))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = ad7606_spi_dev_source_inst &rArr; spi_dev_instance &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dev_instance
</UL>
<BR>[Called By]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_collect_handler_source_inst
</UL>

<P><STRONG><a name="[ac]"></a>adc_collect_event_thread</STRONG> (Thumb, 456 bytes, Stack size 200 bytes, bsp_adc_collect_xxx_handler.o(i.adc_collect_event_thread))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = adc_collect_event_thread &rArr; bsp_adc_collect_xxx_handler_inst &rArr; bsp_adc_collect_xxx_handler_init &rArr; ring_buff_instance &rArr; ring_buff_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_collect_xxx_handler_inst
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_mount_handler
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(i.adc_collect_handler_source_inst)
</UL>
<P><STRONG><a name="[b5]"></a>adc_collect_set_event_thread</STRONG> (Thumb, 108 bytes, Stack size 40 bytes, bsp_adc_collect_xxx_handler.o(i.adc_collect_set_event_thread))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = adc_collect_set_event_thread &rArr; process_adc_collect_xxx_event &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_adc_collect_xxx_event
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp_adc_collect_xxx_handler.o(i.bsp_adc_collect_xxx_handler_init)
</UL>
<P><STRONG><a name="[2b9]"></a>add_gpio_to_rs485</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, rs485_dev.o(i.add_gpio_to_rs485))
<BR><BR>[Called By]<UL><LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handler_inst
</UL>

<P><STRONG><a name="[121]"></a>assert_failed</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, main.o(i.assert_failed))
<BR><BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CRC_Init
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_SlaveConfigSynchro
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_Init
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_ConfigChannel
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_DisableIRQ
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_DeInit
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_DeInit
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_TogglePin
<LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_Erase
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_MassErase
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Erase_Sector
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Program
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Program_Word
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Program_HalfWord
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Program_DoubleWord
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Program_Byte
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SlaveTimer_SetConfig
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit_IT
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit_DMA
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive_IT
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive_DMA
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive_DMA
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_DeInit
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ReadCapturedValue
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Stop_IT
</UL>

<P><STRONG><a name="[205]"></a>async_output_elog</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, elog_async.o(i.async_output_elog))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = async_output_elog &rArr; elog_output_unlock &rArr; elog_port_output_unlock &rArr; safe_mutex_give &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_async_get_buf_used
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_set_output_enabled
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output_unlock
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output_lock
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_output
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;async_output
</UL>

<P><STRONG><a name="[20b]"></a>bsp_ad7606_driver_instance</STRONG> (Thumb, 622 bytes, Stack size 48 bytes, ad7606_driver.o(i.bsp_ad7606_driver_instance))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = bsp_ad7606_driver_instance &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADDLinked_List
</UL>
<BR>[Called By]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_collect_xxx_handler_init
</UL>

<P><STRONG><a name="[20c]"></a>bsp_adc_collect_output_data</STRONG> (Thumb, 148 bytes, Stack size 24 bytes, bsp_adc_collect_xxx_handler.o(i.bsp_adc_collect_output_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = bsp_adc_collect_output_data &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_get_collect_data
</UL>

<P><STRONG><a name="[45]"></a>bsp_adc_collect_xxxx_disable_peripheral_clock</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, system_adaption.o(i.bsp_adc_collect_xxxx_disable_peripheral_clock))
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[44]"></a>bsp_adc_collect_xxxx_enable_peripheral_clock</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, system_adaption.o(i.bsp_adc_collect_xxxx_enable_peripheral_clock))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = bsp_adc_collect_xxxx_enable_peripheral_clock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[42]"></a>bsp_adc_collect_xxxx_interrupt_deinit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, system_adaption.o(i.bsp_adc_collect_xxxx_interrupt_deinit))
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[43]"></a>bsp_adc_collect_xxxx_interrupt_enable</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, system_adaption.o(i.bsp_adc_collect_xxxx_interrupt_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bsp_adc_collect_xxxx_interrupt_enable &rArr; HAL_NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[41]"></a>bsp_adc_collect_xxxx_interrupt_init</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, system_adaption.o(i.bsp_adc_collect_xxxx_interrupt_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = bsp_adc_collect_xxxx_interrupt_init &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[ba]"></a>bsp_led_driver_control</STRONG> (Thumb, 132 bytes, Stack size 40 bytes, bsp_led_driver.o(i.bsp_led_driver_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = bsp_led_driver_control &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp_led_driver.o(i.bsp_led_driver_inst)
</UL>
<P><STRONG><a name="[b9]"></a>bsp_led_driver_deinit</STRONG> (Thumb, 116 bytes, Stack size 32 bytes, bsp_led_driver.o(i.bsp_led_driver_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = bsp_led_driver_deinit &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp_led_driver.o(i.bsp_led_driver_inst)
</UL>
<P><STRONG><a name="[bc]"></a>bsp_led_driver_get_led_which</STRONG> (Thumb, 114 bytes, Stack size 32 bytes, bsp_led_driver.o(i.bsp_led_driver_get_led_which))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = bsp_led_driver_get_led_which &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp_led_driver.o(i.bsp_led_driver_inst)
</UL>
<P><STRONG><a name="[b8]"></a>bsp_led_driver_init</STRONG> (Thumb, 136 bytes, Stack size 32 bytes, bsp_led_driver.o(i.bsp_led_driver_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = bsp_led_driver_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_driver_inst
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp_led_driver.o(i.bsp_led_driver_inst)
</UL>
<P><STRONG><a name="[20f]"></a>bsp_led_driver_inst</STRONG> (Thumb, 308 bytes, Stack size 40 bytes, bsp_led_driver.o(i.bsp_led_driver_inst))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = bsp_led_driver_inst &rArr; bsp_led_driver_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_driver_init
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_driver_inst
</UL>

<P><STRONG><a name="[bb]"></a>bsp_led_driver_set_brightness</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, bsp_led_driver.o(i.bsp_led_driver_set_brightness))
<BR>[Address Reference Count : 1]<UL><LI> bsp_led_driver.o(i.bsp_led_driver_inst)
</UL>
<P><STRONG><a name="[1f7]"></a>bsp_led_handler_control</STRONG> (Thumb, 110 bytes, Stack size 72 bytes, bsp_led_handler.o(i.bsp_led_handler_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = bsp_led_handler_control &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_start_led_indication
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_handler_inst
</UL>

<P><STRONG><a name="[26e]"></a>bsp_led_handler_instance</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, bsp_led_handler.o(i.bsp_led_handler_instance))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = bsp_led_handler_instance
</UL>
<BR>[Called By]<UL><LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_handler_inst
</UL>

<P><STRONG><a name="[210]"></a>bsp_led_handler_register_driver</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, bsp_led_handler.o(i.bsp_led_handler_register_driver))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = bsp_led_handler_register_driver &rArr; _find_led_state_by_which
</UL>
<BR>[Calls]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_find_led_state_by_which
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_allocate_led_state
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADDLinked_List
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_handler_task
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_handler_inst
</UL>

<P><STRONG><a name="[212]"></a>bsp_led_handler_unregister_driver</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, bsp_led_handler.o(i.bsp_led_handler_unregister_driver))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = bsp_led_handler_unregister_driver &rArr; _find_led_state_by_which
</UL>
<BR>[Calls]<UL><LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DELLinked_List
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_free_led_state
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_find_led_state_by_which
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_handler_task
</UL>

<P><STRONG><a name="[215]"></a>calculate_fence_compensation</STRONG> (Thumb, 212 bytes, Stack size 64 bytes, fft_calculate_task.o(i.calculate_fence_compensation))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = calculate_fence_compensation &rArr; __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
</UL>

<P><STRONG><a name="[1e5]"></a>calculate_median_average</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, data_proc_calcu_fun.o(i.calculate_median_average))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = calculate_median_average &rArr; MidFilterBlock &rArr; arm_sort_f32 &rArr; arm_bitonic_sort_f32 &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MidFilterBlock
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__process_amp_result_data
</UL>

<P><STRONG><a name="[216]"></a>calculate_phase_radian</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, fft_calculate_task.o(i.calculate_phase_radian))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = calculate_phase_radian &rArr; __hardfp_atan2f
</UL>
<BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
</UL>

<P><STRONG><a name="[217]"></a>cm_backtrace_call_stack</STRONG> (Thumb, 252 bytes, Stack size 48 bytes, cm_backtrace.o(i.cm_backtrace_call_stack))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = cm_backtrace_call_stack &rArr; get_cur_thread_stack_info &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___14_cm_backtrace_c_13c737ea__cmb_get_sp
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___14_cm_backtrace_c_13c737ea__cmb_get_psp
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_cur_thread_stack_info
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disassembly_ins_is_bl_blx
</UL>
<BR>[Called By]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_call_stack
</UL>

<P><STRONG><a name="[f2]"></a>cm_backtrace_fault</STRONG> (Thumb, 504 bytes, Stack size 88 bytes, cm_backtrace.o(i.cm_backtrace_fault))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = cm_backtrace_fault &rArr; print_call_stack &rArr; cm_backtrace_call_stack &rArr; get_cur_thread_stack_info &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_firmware_info
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___14_cm_backtrace_c_13c737ea__cmb_get_psp
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;statck_del_fpu_regs
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_call_stack
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_cur_thread_stack_info
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_cur_thread_name
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fault_diagnosis
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dump_stack
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>

<P><STRONG><a name="[21c]"></a>cm_backtrace_firmware_info</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, cm_backtrace.o(i.cm_backtrace_firmware_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = cm_backtrace_firmware_info &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
</UL>

<P><STRONG><a name="[222]"></a>cm_backtrace_init</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, cm_backtrace.o(i.cm_backtrace_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = cm_backtrace_init &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[192]"></a>config_elog</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, freertos.o(i.config_elog))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = config_elog &rArr; elog_init &rArr; elog_set_text_color_enabled &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_start
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_set_fmt
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_init
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
</UL>

<P><STRONG><a name="[1d1]"></a>crc32</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, flash_event_fun.o(i.crc32))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = crc32
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_save_sys_info
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__flash_check_value
</UL>

<P><STRONG><a name="[227]"></a>data_proc_handler_init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, data_process_task.o(i.data_proc_handler_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = data_proc_handler_init &rArr; validate_function_pointers &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate_function_pointers
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_proc_handler_inst
</UL>

<P><STRONG><a name="[229]"></a>data_proc_handler_inst</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, data_process_task.o(i.data_proc_handler_inst))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = data_proc_handler_inst &rArr; data_proc_handler_init &rArr; validate_function_pointers &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_proc_handler_init
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_proc_handler_thread
</UL>

<P><STRONG><a name="[bf]"></a>data_proc_handler_thread</STRONG> (Thumb, 190 bytes, Stack size 104 bytes, data_process_task.o(i.data_proc_handler_thread))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = data_proc_handler_thread &rArr; data_proc_handler_inst &rArr; data_proc_handler_init &rArr; validate_function_pointers &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_monitor_event
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_proc_handler_inst
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__data_proc_mount_handler
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(i.data_process_task_instance)
</UL>
<P><STRONG><a name="[17]"></a>data_proc_register_cb_init</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, data_proc_calcu_fun.o(i.data_proc_register_cb_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = data_proc_register_cb_init
</UL>
<BR>[Calls]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_proc_register_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[22c]"></a>data_proc_register_handler</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, data_process_task.o(i.data_proc_register_handler))
<BR><BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_proc_register_cb_init
</UL>

<P><STRONG><a name="[25a]"></a>data_process_event_send</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, data_process_task.o(i.data_process_event_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = data_process_event_send
</UL>
<BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_result_data_send
</UL>

<P><STRONG><a name="[24b]"></a>elog_async_enabled</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, elog_async.o(i.elog_async_enabled))
<BR><BR>[Called By]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_start
</UL>

<P><STRONG><a name="[22e]"></a>elog_async_init</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, elog_async.o(i.elog_async_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = elog_async_init &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueDelete
</UL>
<BR>[Called By]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_init
</UL>

<P><STRONG><a name="[231]"></a>elog_async_output</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, elog_async.o(i.elog_async_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = elog_async_output &rArr; elog_port_output &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_async_output_notice
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;async_put_log
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_output
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>

<P><STRONG><a name="[232]"></a>elog_async_output_notice</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, elog_async.o(i.elog_async_output_notice))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = elog_async_output_notice &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>
<BR>[Called By]<UL><LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_async_output
</UL>

<P><STRONG><a name="[234]"></a>elog_get_filter_tag_lvl</STRONG> (Thumb, 164 bytes, Stack size 40 bytes, elog.o(i.elog_get_filter_tag_lvl))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + In Cycle
<LI>Call Chain = elog_get_filter_tag_lvl &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output_unlock
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output_lock
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>

<P><STRONG><a name="[224]"></a>elog_init</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, elog.o(i.elog_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = elog_init &rArr; elog_set_text_color_enabled &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_async_init
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_set_text_color_enabled
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_set_filter_lvl
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output_lock_enabled
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_set_filter_tag_lvl_default
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_init
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_elog
</UL>

<P><STRONG><a name="[195]"></a>elog_output</STRONG> (Thumb, 868 bytes, Stack size 104 bytes, elog.o(i.elog_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 272 + In Cycle
<LI>Call Chain = elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_strcpy
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_async_output
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output_unlock
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output_lock
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_get_filter_tag_lvl
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fmt_used_and_enabled_u32
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fmt_used_and_enabled_ptr
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fmt_enabled
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_get_time
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_get_t_info
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_get_p_info
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
<LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_send
<LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_handler_init
<LI><a href="#[25e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_flash_event
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_handler_thread
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_register_handler
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_register_cb_init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_save_sys_info
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_read_sys_info
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__flash_check_value
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_led_indicator_on_led_state_changed
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_start_led_indication
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_notify_state_change
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_notify_comm_status_change
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_driver_inst
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_driver_init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_driver_get_led_which
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_driver_deinit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_driver_control
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_buff_instance
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_collect_output_data
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_collect_set_event_thread
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_collect_event_thread
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_adc_collect_xxx_event
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_collect_xxx_handler_inst
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_collect_xxx_handler_init
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_mount_handler
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_ad7606_driver_instance
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_startconvst
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_set_sampling_range
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_set_sampling_mode
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_reset
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_read_data
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_read_busy
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_init
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_deinit
<LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handler_init
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_reg_value_init
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_process_reg_value
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_handle_write_reg_data
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_handle_read_reg_data
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_handle_collect_data_input
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_get_reg_place
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handle_thread
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_rs485_event
<LI><a href="#[273]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_data_origin_input
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_reg_data_callbackfun
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_write_reg
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_read_reg
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__merge_reg_data
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_cmd_0x50_Reply
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_source_inst
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_start
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_set_fmt
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_buff_write
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_buff_reset
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_buff_read
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_buff_init
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_buff_get_item_size
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_buff_deinit
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_buff_adress
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_timer_delete
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_timer_create
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_semaphore_take
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_semaphore_give
<LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_semaphore_delete
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_semaphore_binary_create
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_mutex_create
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_queue_send
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_queue_receive
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_queue_delete
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_queue_create
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_event_group_wait_bits
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_event_group_set_bits
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_event_group_delete
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_event_group_create
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_event_group_clear_bits
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dev_register_callback
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dev_instance
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_interrupt_rxcallbackfun
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dev_write
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dev_timer_callback
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dev_read
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dev_init
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dev_deinit
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__find_spi_dev_for_timner
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_write_pin
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_toggle_pin
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_read_pin
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_dev_instance
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_deinit
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_event_handler_thread
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_proc_handler_thread
<LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_dev_register
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_stop_collect_data
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_start_collect_data
<LI><a href="#[2bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_reg_init
<LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handler_inst
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_save_rs485_reg_value
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_read_rs485_reg_value
<LI><a href="#[25d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_handler_inst
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_result_data_send
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_spi_dev_source_inst
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_handler_inst
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_gpio_interface_inst
<LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_driver_inst
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_task_instance
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_process_task_instance
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_collect_handler_source_inst
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_gpio_source_inst
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_strcpy
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_set_text_color_enabled
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_set_output_enabled
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_set_filter_lvl
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_get_filter_tag_lvl
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fmt_enabled
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_collection_data_thread
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
<LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calcu_handler_inst
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calcu_handler_init
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fft_calcu_mount_handler
<LI><a href="#[257]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fft_send_result
<LI><a href="#[258]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fft_reset_status
<LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fft_check_ready
<LI><a href="#[256]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fft_calculate
<LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calcuate_3rd
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate_function_pointers
</UL>

<P><STRONG><a name="[206]"></a>elog_output_lock</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, elog.o(i.elog_output_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = elog_output_lock &rArr; elog_port_output_lock &rArr; safe_mutex_take &rArr; xQueueSemaphoreTake &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_output_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;async_output_elog
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_get_filter_tag_lvl
</UL>

<P><STRONG><a name="[236]"></a>elog_output_lock_enabled</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, elog.o(i.elog_output_lock_enabled))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = elog_output_lock_enabled &rArr; elog_port_output_unlock &rArr; safe_mutex_give &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_output_unlock
<LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_output_lock
</UL>
<BR>[Called By]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_init
</UL>

<P><STRONG><a name="[209]"></a>elog_output_unlock</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, elog.o(i.elog_output_unlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = elog_output_unlock &rArr; elog_port_output_unlock &rArr; safe_mutex_give &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_output_unlock
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;async_output_elog
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_get_filter_tag_lvl
</UL>

<P><STRONG><a name="[23f]"></a>elog_port_get_p_info</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, elog_port_improved.o(i.elog_port_get_p_info))
<BR><BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>

<P><STRONG><a name="[240]"></a>elog_port_get_t_info</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, elog_port_improved.o(i.elog_port_get_t_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = elog_port_get_t_info
</UL>
<BR>[Calls]<UL><LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pcTaskGetName
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>

<P><STRONG><a name="[23e]"></a>elog_port_get_time</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, elog_port_improved.o(i.elog_port_get_time))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = elog_port_get_time
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>

<P><STRONG><a name="[235]"></a>elog_port_init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, elog_port_improved.o(i.elog_port_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = elog_port_init &rArr; xQueueCreateMutex &rArr; prvInitialiseMutex &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateMutex
</UL>
<BR>[Called By]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_init
</UL>

<P><STRONG><a name="[207]"></a>elog_port_output</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, elog_port_improved.o(i.elog_port_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = elog_port_output &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;async_output_elog
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_async_output
</UL>

<P><STRONG><a name="[245]"></a>elog_port_output_lock</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, elog_port_improved.o(i.elog_port_output_lock))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = elog_port_output_lock &rArr; safe_mutex_take &rArr; xQueueSemaphoreTake &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;safe_mutex_take
</UL>
<BR>[Called By]<UL><LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output_lock_enabled
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output_lock
</UL>

<P><STRONG><a name="[246]"></a>elog_port_output_unlock</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, elog_port_improved.o(i.elog_port_output_unlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = elog_port_output_unlock &rArr; safe_mutex_give &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;safe_mutex_give
</UL>
<BR>[Called By]<UL><LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output_unlock
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output_lock_enabled
</UL>

<P><STRONG><a name="[161]"></a>elog_printf</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, elog_port_improved.o(i.elog_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_vprintf
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_collect_irq_callback
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__adc_collect_event_thread_notify
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_spi_rx_dma_callback_fun
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_busy_irq_callback_fun
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxCpltCallback
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_RxCpltCallback
</UL>

<P><STRONG><a name="[238]"></a>elog_set_filter_lvl</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, elog.o(i.elog_set_filter_lvl))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = elog_set_filter_lvl &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_init
</UL>

<P><STRONG><a name="[225]"></a>elog_set_fmt</STRONG> (Thumb, 80 bytes, Stack size 32 bytes, elog.o(i.elog_set_fmt))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = elog_set_fmt &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_elog
</UL>

<P><STRONG><a name="[208]"></a>elog_set_output_enabled</STRONG> (Thumb, 82 bytes, Stack size 32 bytes, elog.o(i.elog_set_output_enabled))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + In Cycle
<LI>Call Chain = elog_set_output_enabled &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_start
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;async_output_elog
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;async_put_log
</UL>

<P><STRONG><a name="[237]"></a>elog_set_text_color_enabled</STRONG> (Thumb, 82 bytes, Stack size 32 bytes, elog.o(i.elog_set_text_color_enabled))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = elog_set_text_color_enabled &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_init
</UL>

<P><STRONG><a name="[226]"></a>elog_start</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, elog.o(i.elog_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = elog_start &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_async_enabled
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_set_output_enabled
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;config_elog
</UL>

<P><STRONG><a name="[23c]"></a>elog_strcpy</STRONG> (Thumb, 162 bytes, Stack size 40 bytes, elog_utils.o(i.elog_strcpy))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + In Cycle
<LI>Call Chain = elog_strcpy &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>

<P><STRONG><a name="[1dc]"></a>fabs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, fabs.o(i.fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[24c]"></a>fft_calcu_handler_init</STRONG> (Thumb, 472 bytes, Stack size 32 bytes, fft_calculate_task.o(i.fft_calcu_handler_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = fft_calcu_handler_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calcu_handler_inst
</UL>

<P><STRONG><a name="[24d]"></a>fft_calcu_handler_inst</STRONG> (Thumb, 748 bytes, Stack size 32 bytes, fft_calculate_task.o(i.fft_calcu_handler_inst))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = fft_calcu_handler_inst &rArr; fft_calcu_handler_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calcu_handler_init
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_event_handler_thread
</UL>

<P><STRONG><a name="[24f]"></a>fft_calculate_window</STRONG> (Thumb, 1386 bytes, Stack size 104 bytes, fft_calculate_task.o(i.fft_calculate_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = fft_calculate_window &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_array_maxindex
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cmplx_mag_f32
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_f32
<LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rectangle
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_phase_radian
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_fence_compensation
<LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Window
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hanning
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hamming
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FlattopWin
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[256]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fft_calculate
<LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calcuate_3rd
</UL>

<P><STRONG><a name="[27]"></a>fft_check_data_valid</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, system_adaption.o(i.fft_check_data_valid))
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[c1]"></a>fft_collection_data_thread</STRONG> (Thumb, 1538 bytes, Stack size 96 bytes, fft_calculate_task.o(i.fft_collection_data_thread))
<BR><BR>[Stack]<UL><LI>Max Depth = 432<LI>Call Chain = fft_collection_data_thread &rArr; sync_fft_check_ready &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fft_check_ready
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memmove4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> fft_calculate_task.o(i.fft_calcu_handler_init)
</UL>
<P><STRONG><a name="[c2]"></a>fft_event_handler_thread</STRONG> (Thumb, 382 bytes, Stack size 248 bytes, fft_calculate_task.o(i.fft_event_handler_thread))
<BR><BR>[Stack]<UL><LI>Max Depth = 856<LI>Call Chain = fft_event_handler_thread &rArr; sync_fft_calculate &rArr; fft_calcuate_3rd &rArr; fft_calculate_window &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calcu_handler_inst
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fft_calcu_mount_handler
<LI><a href="#[257]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fft_send_result
<LI><a href="#[258]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fft_reset_status
<LI><a href="#[256]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fft_calculate
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(i.fft_calculate_task_instance)
</UL>
<P><STRONG><a name="[26]"></a>fft_get_collect_data</STRONG> (Thumb, 170 bytes, Stack size 32 bytes, system_adaption.o(i.fft_get_collect_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = fft_get_collect_data &rArr; bsp_adc_collect_output_data &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_collect_output_data
<LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[28]"></a>fft_get_collect_data_limit</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, system_adaption.o(i.fft_get_collect_data_limit))
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[29]"></a>fft_result_data_send</STRONG> (Thumb, 214 bytes, Stack size 48 bytes, system_adaption.o(i.fft_result_data_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = fft_result_data_send &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[25a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_process_event_send
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[254]"></a>find_array_maxindex</STRONG> (Thumb, 50 bytes, Stack size 12 bytes, algorithm.o(i.find_array_maxindex))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = find_array_maxindex
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
</UL>

<P><STRONG><a name="[25b]"></a>flash_event_handler_init</STRONG> (Thumb, 336 bytes, Stack size 24 bytes, flash_event_handler.o(i.flash_event_handler_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = flash_event_handler_init &rArr; flash_event_register_cb_init &rArr; flash_register_handler &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_internal_flash_store
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_register_cb_init
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[25d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_handler_inst
</UL>

<P><STRONG><a name="[25d]"></a>flash_event_handler_inst</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, system_adaption.o(i.flash_event_handler_inst))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = flash_event_handler_inst &rArr; flash_event_handler_init &rArr; flash_event_register_cb_init &rArr; flash_register_handler &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_handler_init
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_source_inst
</UL>

<P><STRONG><a name="[4d]"></a>flash_event_register_cb_init</STRONG> (Thumb, 254 bytes, Stack size 24 bytes, flash_event_fun.o(i.flash_event_register_cb_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = flash_event_register_cb_init &rArr; flash_register_handler &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_register_handler
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_handler_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[260]"></a>flash_event_send</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, flash_event_handler.o(i.flash_event_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = flash_event_send &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_save_rs485_reg_value
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_read_rs485_reg_value
</UL>

<P><STRONG><a name="[4c]"></a>flash_read_rs485_reg_value</STRONG> (Thumb, 142 bytes, Stack size 96 bytes, system_adaption.o(i.flash_read_rs485_reg_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = flash_read_rs485_reg_value &rArr; flash_event_send &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_send
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[25f]"></a>flash_register_handler</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, flash_event_handler.o(i.flash_register_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = flash_register_handler &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_register_cb_init
</UL>

<P><STRONG><a name="[4b]"></a>flash_save_rs485_reg_value</STRONG> (Thumb, 140 bytes, Stack size 56 bytes, system_adaption.o(i.flash_save_rs485_reg_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = flash_save_rs485_reg_value &rArr; flash_event_send &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_send
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[a6]"></a>fputc</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, main.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = fputc &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[d6]"></a>get_buff_adress</STRONG> (Thumb, 192 bytes, Stack size 32 bytes, ringbuff.o(i.get_buff_adress))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = get_buff_adress &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ringbuff.o(i.ring_buff_instance)
</UL>
<P><STRONG><a name="[25c]"></a>get_internal_flash_store</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, mcu_flash_driver.o(i.get_internal_flash_store))
<BR><BR>[Called By]<UL><LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_handler_init
</UL>

<P><STRONG><a name="[264]"></a>get_rs485_dev</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, rs485_dev.o(i.get_rs485_dev))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = get_rs485_dev &rArr; strcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handler_init
</UL>

<P><STRONG><a name="[1be]"></a>get_uart_dev</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, uart_dev.o(i.get_uart_dev))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = get_uart_dev &rArr; strcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Select_UartDev
</UL>

<P><STRONG><a name="[2c1]"></a>get_uarthandle</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, mcu_uart_driver.o(i.get_uarthandle))
<BR><BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>

<P><STRONG><a name="[1fb]"></a>gpio_dev_instance</STRONG> (Thumb, 126 bytes, Stack size 32 bytes, mcu_gpio_driver.o(i.gpio_dev_instance))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = gpio_dev_instance &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handler_inst
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_gpio_interface_inst
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_gpio_source_inst
</UL>

<P><STRONG><a name="[c9]"></a>gpio_read_pin</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, mcu_gpio_driver.o(i.gpio_read_pin))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = gpio_read_pin &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_gpio_driver.o(i.gpio_dev_instance)
</UL>
<P><STRONG><a name="[ca]"></a>gpio_toggle_pin</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, mcu_gpio_driver.o(i.gpio_toggle_pin))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = gpio_toggle_pin &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_gpio_driver.o(i.gpio_dev_instance)
</UL>
<P><STRONG><a name="[c8]"></a>gpio_write_pin</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, mcu_gpio_driver.o(i.gpio_write_pin))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = gpio_write_pin &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_gpio_driver.o(i.gpio_dev_instance)
</UL>
<P><STRONG><a name="[c]"></a>internal_flash_erase</STRONG> (Thumb, 108 bytes, Stack size 48 bytes, mcu_flash_driver.o(i.internal_flash_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = internal_flash_erase &rArr; HAL_FLASHEx_Erase &rArr; FLASH_MassErase
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_Erase
<LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Unlock
<LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Lock
<LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSectorSize
<LI><a href="#[267]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSector
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_flash_driver.o(.data)
</UL>
<P><STRONG><a name="[b]"></a>internal_flash_read</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, mcu_flash_driver.o(i.internal_flash_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = internal_flash_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_flash_driver.o(.data)
</UL>
<P><STRONG><a name="[a]"></a>internal_flash_write</STRONG> (Thumb, 118 bytes, Stack size 40 bytes, mcu_flash_driver.o(i.internal_flash_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = internal_flash_write &rArr; HAL_FLASHEx_Erase &rArr; FLASH_MassErase
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_Erase
<LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Unlock
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Program
<LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Lock
<LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[267]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSector
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_flash_driver.o(.data)
</UL>
<P><STRONG><a name="[bd]"></a>led_handler_task</STRONG> (Thumb, 182 bytes, Stack size 80 bytes, bsp_led_handler.o(i.led_handler_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = led_handler_task &rArr; bsp_led_handler_unregister_driver &rArr; _find_led_state_by_which
</UL>
<BR>[Calls]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetNextNode
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_handler_unregister_driver
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_handler_register_driver
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_update_led_state
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_led_handler_control_internal
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp_led_handler.o(i.bsp_led_handler_instance)
</UL>
<P><STRONG><a name="[251]"></a>lpf_fir_instance</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, lpf_fir_alogorithm.o(i.lpf_fir_instance))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = lpf_fir_instance &rArr; lpf_fir_init &rArr; arm_fir_init_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lpf_fir_init
</UL>
<BR>[Called By]<UL><LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_task_instance
</UL>

<P><STRONG><a name="[91]"></a>main</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 472<LI>Call Chain = main &rArr; MX_FREERTOS_Init &rArr; system_source_inst &rArr; led_handler_inst &rArr; led_driver_inst &rArr; bsp_led_driver_inst &rArr; bsp_led_driver_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CRC_Init
<LI><a href="#[272]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osKernelStart
<LI><a href="#[271]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osKernelInitialize
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_init
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM10_Init
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[273]"></a>modbus_data_origin_input</STRONG> (Thumb, 100 bytes, Stack size 48 bytes, reg_adress_msg.o(i.modbus_data_origin_input))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = modbus_data_origin_input &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handle_send
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_modbus_data
</UL>

<P><STRONG><a name="[275]"></a>modbus_init</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, modbus_rtu.o(i.modbus_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = modbus_init &rArr; register_timer
</UL>
<BR>[Calls]<UL><LI><a href="#[276]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;register_timer
</UL>
<BR>[Called By]<UL><LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handler_init
</UL>

<P><STRONG><a name="[1a7]"></a>modbus_read_reg</STRONG> (Thumb, 104 bytes, Stack size 48 bytes, reg_adress_msg.o(i.modbus_read_reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = modbus_read_reg &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handle_send
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__merge_reg_data
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_cmd_0x03_Reply
</UL>

<P><STRONG><a name="[1a0]"></a>modbus_rtu_check_fun_code</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, modbus_rtu.o(i.modbus_rtu_check_fun_code))
<BR><BR>[Called By]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_RTU_cmd_Recv
</UL>

<P><STRONG><a name="[19f]"></a>modbus_rtu_crc16</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, modbus_rtu.o(i.modbus_rtu_crc16))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = modbus_rtu_crc16
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_cmd_0x10_Reply
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_cmd_0x06_Reply
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_cmd_0x03_Reply
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_RTU_cmd_Recv
</UL>

<P><STRONG><a name="[ce]"></a>modbus_switch_recv_status</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, modbus_rtu.o(i.modbus_switch_recv_status))
<BR>[Address Reference Count : 1]<UL><LI> modbus_rtu.o(i.modbus_init)
</UL>
<P><STRONG><a name="[1a8]"></a>modbus_write_reg</STRONG> (Thumb, 100 bytes, Stack size 48 bytes, reg_adress_msg.o(i.modbus_write_reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = modbus_write_reg &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handle_send
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_cmd_0x10_Reply
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_cmd_0x06_Reply
</UL>

<P><STRONG><a name="[1bf]"></a>osDelay</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, cmsis_os2.o(i.osDelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = osDelay &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_IPSR
<LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartDefaultTask
</UL>

<P><STRONG><a name="[271]"></a>osKernelInitialize</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, cmsis_os2.o(i.osKernelInitialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = osKernelInitialize
</UL>
<BR>[Calls]<UL><LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_IPSR
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[272]"></a>osKernelStart</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, cmsis_os2.o(i.osKernelStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = osKernelStart &rArr; vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreateStatic &rArr; prvInitialiseNewTask
</UL>
<BR>[Calls]<UL><LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_IPSR
<LI><a href="#[278]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[193]"></a>osThreadNew</STRONG> (Thumb, 184 bytes, Stack size 48 bytes, cmsis_os2.o(i.osThreadNew))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = osThreadNew &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_IPSR
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
</UL>

<P><STRONG><a name="[2a]"></a>os_critical_enter</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, os_critical.o(i.os_critical_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = os_critical_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Address Reference Count : 2]<UL><LI> system_adaption.o(i.led_handler_inst)
<LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[46]"></a>os_critical_enter_isr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, os_critical.o(i.os_critical_enter_isr))
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[2b]"></a>os_critical_exit</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, os_critical.o(i.os_critical_exit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = os_critical_exit
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
</UL>
<BR>[Address Reference Count : 2]<UL><LI> system_adaption.o(i.led_handler_inst)
<LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[47]"></a>os_critical_exit_isr</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, os_critical.o(i.os_critical_exit_isr))
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[23]"></a>os_event_group_clear_bits</STRONG> (Thumb, 56 bytes, Stack size 32 bytes, os_event_group.o(i.os_event_group_clear_bits))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = os_event_group_clear_bits &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupClearBits
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[20]"></a>os_event_group_create</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, os_event_group.o(i.os_event_group_create))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = os_event_group_create &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[27b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupCreate
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[21]"></a>os_event_group_delete</STRONG> (Thumb, 50 bytes, Stack size 24 bytes, os_event_group.o(i.os_event_group_delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = os_event_group_delete &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vEventGroupDelete
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[22]"></a>os_event_group_set_bits</STRONG> (Thumb, 56 bytes, Stack size 32 bytes, os_event_group.o(i.os_event_group_set_bits))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = os_event_group_set_bits &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupSetBits
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[24]"></a>os_event_group_wait_bits</STRONG> (Thumb, 96 bytes, Stack size 56 bytes, os_event_group.o(i.os_event_group_wait_bits))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = os_event_group_wait_bits &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[3e]"></a>os_mutex_create</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, os_semaphore.o(i.os_mutex_create))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = os_mutex_create &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateMutex
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[12]"></a>os_queue_create</STRONG> (Thumb, 96 bytes, Stack size 32 bytes, os_queue.o(i.os_queue_create))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = os_queue_create &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[13]"></a>os_queue_delete</STRONG> (Thumb, 50 bytes, Stack size 24 bytes, os_queue.o(i.os_queue_delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = os_queue_delete &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueDelete
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[15]"></a>os_queue_receive</STRONG> (Thumb, 100 bytes, Stack size 32 bytes, os_queue.o(i.os_queue_receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = os_queue_receive &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[14]"></a>os_queue_send</STRONG> (Thumb, 128 bytes, Stack size 32 bytes, os_queue.o(i.os_queue_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = os_queue_send &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[19]"></a>os_semaphore_binary_create</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, os_semaphore.o(i.os_semaphore_binary_create))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = os_semaphore_binary_create &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[1a]"></a>os_semaphore_delete</STRONG> (Thumb, 50 bytes, Stack size 24 bytes, os_semaphore.o(i.os_semaphore_delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = os_semaphore_delete &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueDelete
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[38]"></a>os_semaphore_give</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, os_semaphore.o(i.os_semaphore_give))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = os_semaphore_give &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[1c]"></a>os_semaphore_give_fromisr</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, os_semaphore.o(i.os_semaphore_give_fromisr))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = os_semaphore_give_fromisr &rArr; xQueueGiveFromISR &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGiveFromISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[1b]"></a>os_semaphore_take</STRONG> (Thumb, 84 bytes, Stack size 32 bytes, os_semaphore.o(i.os_semaphore_take))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = os_semaphore_take &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[39]"></a>os_semaphore_take_fromisr</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, os_semaphore.o(i.os_semaphore_take_fromisr))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = os_semaphore_take_fromisr &rArr; xQueueReceiveFromISR &rArr; prvCopyDataFromQueue &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[280]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceiveFromISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[18]"></a>os_task_create</STRONG> (Thumb, 46 bytes, Stack size 40 bytes, os_task.o(i.os_task_create))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = os_task_create &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[4a]"></a>os_task_delete</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, os_task.o(i.os_task_delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = os_task_delete &rArr; vTaskDelete &rArr; prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[1f]"></a>os_task_get_handle</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, os_task.o(i.os_task_get_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = os_task_get_handle
</UL>
<BR>[Calls]<UL><LI><a href="#[282]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetCurrentTaskHandle
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[40]"></a>os_task_notify_clear</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, os_task.o(i.os_task_notify_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = os_task_notify_clear &rArr; ulTaskNotifyValueClear
</UL>
<BR>[Calls]<UL><LI><a href="#[283]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskNotifyValueClear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[1d]"></a>os_task_notify_give</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, os_task.o(i.os_task_notify_give))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = os_task_notify_give &rArr; xTaskGenericNotify
</UL>
<BR>[Calls]<UL><LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGenericNotify
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[3f]"></a>os_task_notify_give_fromisr</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, os_task.o(i.os_task_notify_give_fromisr))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = os_task_notify_give_fromisr &rArr; vTaskNotifyGiveFromISR &rArr; vPortValidateInterruptPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskNotifyGiveFromISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[1e]"></a>os_task_notify_take</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, os_task.o(i.os_task_notify_take))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = os_task_notify_take &rArr; ulTaskNotifyTake &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskNotifyTake
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[3a]"></a>os_timer_create</STRONG> (Thumb, 190 bytes, Stack size 48 bytes, os_timer.o(i.os_timer_create))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = os_timer_create &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreate
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[3b]"></a>os_timer_delete</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, os_timer.o(i.os_timer_delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = os_timer_delete &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[3c]"></a>os_timer_start_fromisr</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, os_timer.o(i.os_timer_start_fromisr))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = os_timer_start_fromisr &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCountFromISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[3d]"></a>os_timer_stop_fromisr</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, os_timer.o(i.os_timer_stop_fromisr))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = os_timer_stop_fromisr &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[247]"></a>pcTaskGetName</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, tasks.o(i.pcTaskGetName))
<BR><BR>[Called By]<UL><LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_get_t_info
</UL>

<P><STRONG><a name="[22b]"></a>process_monitor_event</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, data_process_task.o(i.process_monitor_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = process_monitor_event
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_proc_handler_thread
</UL>

<P><STRONG><a name="[2c]"></a>pvPortMalloc</STRONG> (Thumb, 316 bytes, Stack size 24 bytes, heap_4.o(i.pvPortMalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationMallocFailedHook
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertBlockIntoFreeList
<LI><a href="#[2b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHeapInit
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreate
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[27b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupCreate
<LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_driver_inst
</UL>
<BR>[Address Reference Count : 2]<UL><LI> system_adaption.o(i.led_handler_inst)
<LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[2d7]"></a>pvTaskIncrementMutexHeldCount</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, tasks.o(i.pvTaskIncrementMutexHeldCount))
<BR><BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
</UL>

<P><STRONG><a name="[2a0]"></a>pxPortInitialiseStack</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, port.o(i.pxPortInitialiseStack))
<BR><BR>[Called By]<UL><LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
</UL>

<P><STRONG><a name="[252]"></a>rectangle</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, fft_calculate_task.o(i.rectangle))
<BR><BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
</UL>

<P><STRONG><a name="[276]"></a>register_timer</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, rs485_event_handler.o(i.register_timer))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = register_timer
</UL>
<BR>[Called By]<UL><LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_init
</UL>

<P><STRONG><a name="[2c0]"></a>register_uart_funtion</STRONG> (Thumb, 76 bytes, Stack size 36 bytes, mcu_uart_driver.o(i.register_uart_funtion))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = register_uart_funtion
</UL>
<BR>[Called By]<UL><LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_dev_register
</UL>

<P><STRONG><a name="[d2]"></a>ring_buff_deinit</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, ringbuff.o(i.ring_buff_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = ring_buff_deinit &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ringbuff.o(i.ring_buff_instance)
</UL>
<P><STRONG><a name="[d7]"></a>ring_buff_dma_writes_index</STRONG> (Thumb, 164 bytes, Stack size 32 bytes, ringbuff.o(i.ring_buff_dma_writes_index))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ring_buff_dma_writes_index
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ringbuff.o(i.ring_buff_instance)
</UL>
<P><STRONG><a name="[d8]"></a>ring_buff_get_item_size</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, ringbuff.o(i.ring_buff_get_item_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = ring_buff_get_item_size &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ringbuff.o(i.ring_buff_instance)
</UL>
<P><STRONG><a name="[d1]"></a>ring_buff_init</STRONG> (Thumb, 268 bytes, Stack size 24 bytes, ringbuff.o(i.ring_buff_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = ring_buff_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_buff_instance
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ringbuff.o(i.ring_buff_instance)
</UL>
<P><STRONG><a name="[20e]"></a>ring_buff_instance</STRONG> (Thumb, 422 bytes, Stack size 40 bytes, ringbuff.o(i.ring_buff_instance))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = ring_buff_instance &rArr; ring_buff_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_buff_init
</UL>
<BR>[Called By]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_collect_xxx_handler_init
</UL>

<P><STRONG><a name="[d4]"></a>ring_buff_read</STRONG> (Thumb, 400 bytes, Stack size 56 bytes, ringbuff.o(i.ring_buff_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = ring_buff_read &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ringbuff.o(i.ring_buff_instance)
</UL>
<P><STRONG><a name="[d5]"></a>ring_buff_reset</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, ringbuff.o(i.ring_buff_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = ring_buff_reset &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ringbuff.o(i.ring_buff_instance)
</UL>
<P><STRONG><a name="[d3]"></a>ring_buff_write</STRONG> (Thumb, 394 bytes, Stack size 56 bytes, ringbuff.o(i.ring_buff_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = ring_buff_write &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ringbuff.o(i.ring_buff_instance)
</UL>
<P><STRONG><a name="[2b7]"></a>rs485_event_fun_init</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, rs485_event_handler.o(i.rs485_event_fun_init))
<BR><BR>[Called By]<UL><LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handler_init
</UL>

<P><STRONG><a name="[274]"></a>rs485_event_handle_send</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, rs485_event_handler.o(i.rs485_event_handle_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rs485_event_handle_send
</UL>
<BR>[Called By]<UL><LI><a href="#[273]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_data_origin_input
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_write_reg
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_read_reg
</UL>

<P><STRONG><a name="[2b6]"></a>rs485_event_handler_init</STRONG> (Thumb, 668 bytes, Stack size 32 bytes, rs485_event_handler.o(i.rs485_event_handler_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = rs485_event_handler_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[2b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_fun_init
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_rs485_dev
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADDRS485DEVICE
<LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_init
</UL>
<BR>[Called By]<UL><LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handler_inst
</UL>

<P><STRONG><a name="[2b8]"></a>rs485_event_handler_inst</STRONG> (Thumb, 208 bytes, Stack size 16 bytes, system_adaption.o(i.rs485_event_handler_inst))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = rs485_event_handler_inst &rArr; rs485_event_handler_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handler_init
<LI><a href="#[2b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;add_gpio_to_rs485
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_dev_instance
<LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_dev_register
<LI><a href="#[2bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_reg_init
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_source_inst
</UL>

<P><STRONG><a name="[2be]"></a>rs485_instance_reg_msg</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, rs485_event_handler.o(i.rs485_instance_reg_msg))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rs485_instance_reg_msg
</UL>
<BR>[Called By]<UL><LI><a href="#[2bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_reg_init
</UL>

<P><STRONG><a name="[2bb]"></a>rs485_reg_init</STRONG> (Thumb, 2458 bytes, Stack size 24 bytes, system_adaption.o(i.rs485_reg_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = rs485_reg_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_instance_reg_msg
</UL>
<BR>[Called By]<UL><LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handler_inst
</UL>

<P><STRONG><a name="[1fd]"></a>spi_dev_instance</STRONG> (Thumb, 278 bytes, Stack size 56 bytes, mcu_spi_driver.o(i.spi_dev_instance))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = spi_dev_instance &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADDLinked_List
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_spi_dev_source_inst
</UL>

<P><STRONG><a name="[e5]"></a>spi_dev_read_dma</STRONG> (Thumb, 148 bytes, Stack size 24 bytes, mcu_spi_driver.o(i.spi_dev_read_dma))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = spi_dev_read_dma
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_spi_driver.o(i.spi_dev_instance)
</UL>
<P><STRONG><a name="[e6]"></a>spi_dev_register_callback</STRONG> (Thumb, 178 bytes, Stack size 40 bytes, mcu_spi_driver.o(i.spi_dev_register_callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = spi_dev_register_callback &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_spi_driver.o(i.spi_dev_instance)
</UL>
<P><STRONG><a name="[1db]"></a>sqrt</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, sqrt.o(i.sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[194]"></a>system_source_inst</STRONG> (Thumb, 198 bytes, Stack size 16 bytes, system_adaption.o(i.system_source_inst))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = system_source_inst &rArr; led_handler_inst &rArr; led_driver_inst &rArr; bsp_led_driver_inst &rArr; bsp_led_driver_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handler_inst
<LI><a href="#[25d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_handler_inst
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_handler_inst
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_task_instance
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_process_task_instance
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_collect_handler_source_inst
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
</UL>

<P><STRONG><a name="[19e]"></a>timer_start</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, rs485_event_handler.o(i.timer_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_start
</UL>
<BR>[Called By]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_RTU_cmd_Recv
</UL>

<P><STRONG><a name="[48]"></a>timer_start_collect_data</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, system_adaption.o(i.timer_start_collect_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = timer_start_collect_data &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[49]"></a>timer_stop_collect_data</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, system_adaption.o(i.timer_stop_collect_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = timer_stop_collect_data &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Stop_IT
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[2ba]"></a>uart_dev_register</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, system_adaption.o(i.uart_dev_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = uart_dev_register &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[2c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;register_uart_funtion
</UL>
<BR>[Called By]<UL><LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handler_inst
</UL>

<P><STRONG><a name="[17e]"></a>uart_led_indicator_on_data_received</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, uart_led_indicator.o(i.uart_led_indicator_on_data_received))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = uart_led_indicator_on_data_received &rArr; _start_led_indication &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_start_led_indication
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_notify_state_change
</UL>
<BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[a9]"></a>uart_led_indicator_on_led_state_changed</STRONG> (Thumb, 100 bytes, Stack size 32 bytes, uart_led_indicator.o(i.uart_led_indicator_on_led_state_changed))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = uart_led_indicator_on_led_state_changed &rArr; _notify_state_change &rArr; _notify_comm_status_change &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_notify_state_change
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uart_led_indicator.o(i._start_led_indication)
</UL>
<P><STRONG><a name="[286]"></a>ulTaskNotifyTake</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, tasks.o(i.ulTaskNotifyTake))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ulTaskNotifyTake &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_task_notify_take
</UL>

<P><STRONG><a name="[283]"></a>ulTaskNotifyValueClear</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, tasks.o(i.ulTaskNotifyValueClear))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ulTaskNotifyValueClear
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_task_notify_clear
</UL>

<P><STRONG><a name="[28d]"></a>uxListRemove</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, list.o(i.uxListRemove))
<BR><BR>[Called By]<UL><LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGenericNotify
<LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskNotifyGiveFromISR
<LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[296]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[2d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityInherit
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPriorityDisinheritAfterTimeout
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskRemoveFromUnorderedEventList
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>

<P><STRONG><a name="[2d0]"></a>uxTaskResetEventItemValue</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, tasks.o(i.uxTaskResetEventItemValue))
<BR><BR>[Called By]<UL><LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
</UL>

<P><STRONG><a name="[2cb]"></a>vApplicationGetIdleTaskMemory</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, cmsis_os2.o(i.vApplicationGetIdleTaskMemory))
<BR><BR>[Called By]<UL><LI><a href="#[278]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[2da]"></a>vApplicationGetTimerTaskMemory</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, cmsis_os2.o(i.vApplicationGetTimerTaskMemory))
<BR><BR>[Called By]<UL><LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[2b4]"></a>vApplicationMallocFailedHook</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, freertos.o(i.vApplicationMallocFailedHook))
<BR><BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[2ce]"></a>vApplicationStackOverflowHook</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, freertos.o(i.vApplicationStackOverflowHook))
<BR><BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
</UL>

<P><STRONG><a name="[27c]"></a>vEventGroupDelete</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, event_groups.o(i.vEventGroupDelete))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = vEventGroupDelete &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskRemoveFromUnorderedEventList
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
</UL>
<BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_event_group_delete
</UL>

<P><STRONG><a name="[293]"></a>vListInitialise</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, list.o(i.vListInitialise))
<BR><BR>[Called By]<UL><LI><a href="#[27b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupCreate
<LI><a href="#[291]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseTaskLists
<LI><a href="#[29d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>

<P><STRONG><a name="[29f]"></a>vListInitialiseItem</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, list.o(i.vListInitialiseItem))
<BR><BR>[Called By]<UL><LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
<LI><a href="#[2a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTimer
</UL>

<P><STRONG><a name="[28f]"></a>vListInsert</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, list.o(i.vListInsert))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
</UL>

<P><STRONG><a name="[28e]"></a>vListInsertEnd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, list.o(i.vListInsertEnd))
<BR><BR>[Called By]<UL><LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGenericNotify
<LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskNotifyGiveFromISR
<LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[2d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityInherit
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPriorityDisinheritAfterTimeout
<LI><a href="#[2c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskRemoveFromUnorderedEventList
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnUnorderedEventList
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
</UL>

<P><STRONG><a name="[265]"></a>vPortEnterCritical</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, port.o(i.vPortEnterCritical))
<BR><BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGenericNotify
<LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[283]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskNotifyValueClear
<LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskNotifyTake
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
<LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupClearBits
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_critical_enter
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;internal_flash_write
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;internal_flash_erase
<LI><a href="#[296]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[29d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>

<P><STRONG><a name="[26a]"></a>vPortExitCritical</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, port.o(i.vPortExitCritical))
<BR><BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGenericNotify
<LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[283]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskNotifyValueClear
<LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskNotifyTake
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
<LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupClearBits
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_critical_exit
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;internal_flash_write
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;internal_flash_erase
<LI><a href="#[296]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[29d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>

<P><STRONG><a name="[2d]"></a>vPortFree</STRONG> (Thumb, 150 bytes, Stack size 16 bytes, heap_4.o(i.vPortFree))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertBlockIntoFreeList
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueDelete
<LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vEventGroupDelete
<LI><a href="#[297]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDeleteTCB
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>
<BR>[Address Reference Count : 2]<UL><LI> system_adaption.o(i.led_handler_inst)
<LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[2d1]"></a>vPortSetupTimerInterrupt</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, port.o(i.vPortSetupTimerInterrupt))
<BR><BR>[Called By]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[2c3]"></a>vPortValidateInterruptPriority</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, port.o(i.vPortValidateInterruptPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = vPortValidateInterruptPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[2c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortGetIPSR
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGiveFromISR
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCountFromISR
<LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskNotifyGiveFromISR
<LI><a href="#[280]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceiveFromISR
</UL>

<P><STRONG><a name="[295]"></a>vQueueAddToRegistry</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, queue.o(i.vQueueAddToRegistry))
<BR><BR>[Called By]<UL><LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>

<P><STRONG><a name="[230]"></a>vQueueDelete</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, queue.o(i.vQueueDelete))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = vQueueDelete &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueUnregisterQueue
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
</UL>
<BR>[Called By]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_semaphore_delete
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_queue_delete
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_async_init
</UL>

<P><STRONG><a name="[2c5]"></a>vQueueUnregisterQueue</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, queue.o(i.vQueueUnregisterQueue))
<BR><BR>[Called By]<UL><LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueDelete
</UL>

<P><STRONG><a name="[2ab]"></a>vQueueWaitForMessageRestricted</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, queue.o(i.vQueueWaitForMessageRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = vQueueWaitForMessageRestricted &rArr; vTaskPlaceOnEventListRestricted &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[2c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
</UL>
<BR>[Called By]<UL><LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
</UL>

<P><STRONG><a name="[259]"></a>vTaskDelay</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, tasks.o(i.vTaskDelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_get_collect_data
</UL>

<P><STRONG><a name="[281]"></a>vTaskDelete</STRONG> (Thumb, 172 bytes, Stack size 16 bytes, tasks.o(i.vTaskDelete))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = vTaskDelete &rArr; prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
<LI><a href="#[297]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDeleteTCB
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_task_delete
</UL>

<P><STRONG><a name="[2d5]"></a>vTaskInternalSetTimeOutState</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, tasks.o(i.vTaskInternalSetTimeOutState))
<BR><BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
</UL>

<P><STRONG><a name="[2b1]"></a>vTaskMissedYield</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tasks.o(i.vTaskMissedYield))
<BR><BR>[Called By]<UL><LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
</UL>

<P><STRONG><a name="[261]"></a>vTaskName</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tasks.o(i.vTaskName))
<BR><BR>[Called By]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_cur_thread_name
</UL>

<P><STRONG><a name="[285]"></a>vTaskNotifyGiveFromISR</STRONG> (Thumb, 216 bytes, Stack size 24 bytes, tasks.o(i.vTaskNotifyGiveFromISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = vTaskNotifyGiveFromISR &rArr; vPortValidateInterruptPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_task_notify_give_fromisr
</UL>

<P><STRONG><a name="[2c8]"></a>vTaskPlaceOnEventList</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, tasks.o(i.vTaskPlaceOnEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[2c6]"></a>vTaskPlaceOnEventListRestricted</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, tasks.o(i.vTaskPlaceOnEventListRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = vTaskPlaceOnEventListRestricted &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
</UL>
<BR>[Called By]<UL><LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>

<P><STRONG><a name="[2c9]"></a>vTaskPlaceOnUnorderedEventList</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, tasks.o(i.vTaskPlaceOnUnorderedEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = vTaskPlaceOnUnorderedEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
</UL>
<BR>[Called By]<UL><LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
</UL>

<P><STRONG><a name="[2ca]"></a>vTaskPriorityDisinheritAfterTimeout</STRONG> (Thumb, 198 bytes, Stack size 32 bytes, tasks.o(i.vTaskPriorityDisinheritAfterTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = vTaskPriorityDisinheritAfterTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
</UL>

<P><STRONG><a name="[2c2]"></a>vTaskRemoveFromUnorderedEventList</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, tasks.o(i.vTaskRemoveFromUnorderedEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = vTaskRemoveFromUnorderedEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupSetBits
<LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vEventGroupDelete
</UL>

<P><STRONG><a name="[262]"></a>vTaskStackAddr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tasks.o(i.vTaskStackAddr))
<BR><BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_cur_thread_stack_info
</UL>

<P><STRONG><a name="[263]"></a>vTaskStackSize</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tasks.o(i.vTaskStackSize))
<BR><BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_cur_thread_stack_info
</UL>

<P><STRONG><a name="[278]"></a>vTaskStartScheduler</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, tasks.o(i.vTaskStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreateStatic &rArr; prvInitialiseNewTask
</UL>
<BR>[Calls]<UL><LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
<LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationGetIdleTaskMemory
<LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
</UL>
<BR>[Called By]<UL><LI><a href="#[272]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osKernelStart
</UL>

<P><STRONG><a name="[2a9]"></a>vTaskSuspendAll</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, tasks.o(i.vTaskSuspendAll))
<BR><BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
<LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupSetBits
<LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vEventGroupDelete
<LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
</UL>

<P><STRONG><a name="[f1]"></a>vTaskSwitchContext</STRONG> (Thumb, 178 bytes, Stack size 16 bytes, tasks.o(i.vTaskSwitchContext))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = vTaskSwitchContext
</UL>
<BR>[Calls]<UL><LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationStackOverflowHook
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>

<P><STRONG><a name="[16]"></a>write_modbus_data</STRONG> (Thumb, 410 bytes, Stack size 24 bytes, system_adaption.o(i.write_modbus_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = write_modbus_data &rArr; modbus_data_origin_input &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[273]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_data_origin_input
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[27a]"></a>xEventGroupClearBits</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, event_groups.o(i.xEventGroupClearBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xEventGroupClearBits
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_event_group_clear_bits
</UL>

<P><STRONG><a name="[27b]"></a>xEventGroupCreate</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, event_groups.o(i.xEventGroupCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = xEventGroupCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_event_group_create
</UL>

<P><STRONG><a name="[27d]"></a>xEventGroupSetBits</STRONG> (Thumb, 206 bytes, Stack size 48 bytes, event_groups.o(i.xEventGroupSetBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = xEventGroupSetBits &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskRemoveFromUnorderedEventList
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_event_group_set_bits
</UL>

<P><STRONG><a name="[27e]"></a>xEventGroupWaitBits</STRONG> (Thumb, 338 bytes, Stack size 64 bytes, event_groups.o(i.xEventGroupWaitBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = xEventGroupWaitBits &rArr; vTaskPlaceOnUnorderedEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnUnorderedEventList
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxTaskResetEventItemValue
<LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTestWaitCondition
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_event_group_wait_bits
</UL>

<P><STRONG><a name="[2cd]"></a>xPortStartScheduler</STRONG> (Thumb, 330 bytes, Stack size 16 bytes, port.o(i.xPortStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xPortStartScheduler
</UL>
<BR>[Calls]<UL><LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortSetupTimerInterrupt
<LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___6_port_c_39a90d8d__prvEnableVFP
<LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___6_port_c_39a90d8d__prvStartFirstTask
</UL>
<BR>[Called By]<UL><LI><a href="#[278]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[1c1]"></a>xPortSysTickHandler</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, port.o(i.xPortSysTickHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = xPortSysTickHandler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[248]"></a>xQueueCreateMutex</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, queue.o(i.xQueueCreateMutex))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = xQueueCreateMutex &rArr; prvInitialiseMutex &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseMutex
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_mutex_create
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_init
</UL>

<P><STRONG><a name="[22f]"></a>xQueueGenericCreate</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, queue.o(i.xQueueGenericCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = xQueueGenericCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[29c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateMutex
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_semaphore_binary_create
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_queue_create
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_async_init
</UL>

<P><STRONG><a name="[294]"></a>xQueueGenericCreateStatic</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, queue.o(i.xQueueGenericCreateStatic))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = xQueueGenericCreateStatic &rArr; prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[29c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
</UL>
<BR>[Called By]<UL><LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>

<P><STRONG><a name="[29d]"></a>xQueueGenericReset</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, queue.o(i.xQueueGenericReset))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[29c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
</UL>

<P><STRONG><a name="[233]"></a>xQueueGenericSend</STRONG> (Thumb, 420 bytes, Stack size 56 bytes, queue.o(i.xQueueGenericSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[2d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_semaphore_give
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_queue_send
<LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseMutex
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_async_output_notice
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;safe_mutex_give
</UL>

<P><STRONG><a name="[17f]"></a>xQueueGenericSendFromISR</STRONG> (Thumb, 252 bytes, Stack size 40 bytes, queue.o(i.xQueueGenericSendFromISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = xQueueGenericSendFromISR &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit
</UL>
<BR>[Calls]<UL><LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
</UL>

<P><STRONG><a name="[18c]"></a>xQueueGiveFromISR</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, queue.o(i.xQueueGiveFromISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = xQueueGiveFromISR &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
<LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_semaphore_give_fromisr
</UL>

<P><STRONG><a name="[27f]"></a>xQueueReceive</STRONG> (Thumb, 356 bytes, Stack size 48 bytes, queue.o(i.xQueueReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = xQueueReceive &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[2d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataFromQueue
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_recv
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_queue_receive
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>

<P><STRONG><a name="[280]"></a>xQueueReceiveFromISR</STRONG> (Thumb, 202 bytes, Stack size 40 bytes, queue.o(i.xQueueReceiveFromISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = xQueueReceiveFromISR &rArr; prvCopyDataFromQueue &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataFromQueue
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_semaphore_take_fromisr
</UL>

<P><STRONG><a name="[204]"></a>xQueueSemaphoreTake</STRONG> (Thumb, 434 bytes, Stack size 40 bytes, queue.o(i.xQueueSemaphoreTake))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = xQueueSemaphoreTake &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[2d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityInherit
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPriorityDisinheritAfterTimeout
<LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[2d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[2d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvTaskIncrementMutexHeldCount
<LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[2d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetDisinheritPriorityAfterTimeout
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_send
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_semaphore_take
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;async_output
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;safe_mutex_take
</UL>

<P><STRONG><a name="[2d6]"></a>xTaskCheckForTimeOut</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, tasks.o(i.xTaskCheckForTimeOut))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTaskCheckForTimeOut
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[2d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[200]"></a>xTaskCreate</STRONG> (Thumb, 100 bytes, Stack size 72 bytes, tasks.o(i.xTaskCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
<LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osThreadNew
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_task_create
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_task_instance
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_process_task_instance
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_collect_handler_source_inst
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_async_init
</UL>

<P><STRONG><a name="[279]"></a>xTaskCreateStatic</STRONG> (Thumb, 186 bytes, Stack size 56 bytes, tasks.o(i.xTaskCreateStatic))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = xTaskCreateStatic &rArr; prvInitialiseNewTask
</UL>
<BR>[Calls]<UL><LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
<LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>
<BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osThreadNew
<LI><a href="#[278]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
<LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[284]"></a>xTaskGenericNotify</STRONG> (Thumb, 282 bytes, Stack size 32 bytes, tasks.o(i.xTaskGenericNotify))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = xTaskGenericNotify
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_task_notify_give
</UL>

<P><STRONG><a name="[282]"></a>xTaskGetCurrentTaskHandle</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetCurrentTaskHandle))
<BR><BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_task_get_handle
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;safe_mutex_take
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;safe_mutex_give
</UL>

<P><STRONG><a name="[1c0]"></a>xTaskGetSchedulerState</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetSchedulerState))
<BR><BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
<LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_output_unlock
<LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_output_lock
</UL>

<P><STRONG><a name="[2ac]"></a>xTaskGetTickCount</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetTickCount))
<BR><BR>[Called By]<UL><LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
</UL>

<P><STRONG><a name="[289]"></a>xTaskGetTickCountFromISR</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, tasks.o(i.xTaskGetTickCountFromISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = xTaskGetTickCountFromISR &rArr; vPortValidateInterruptPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_timer_start_fromisr
</UL>

<P><STRONG><a name="[2d4]"></a>xTaskIncrementTick</STRONG> (Thumb, 284 bytes, Stack size 24 bytes, tasks.o(i.xTaskIncrementTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
</UL>

<P><STRONG><a name="[29a]"></a>xTaskPriorityDisinherit</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, tasks.o(i.xTaskPriorityDisinherit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTaskPriorityDisinherit
</UL>
<BR>[Calls]<UL><LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
</UL>

<P><STRONG><a name="[2d8]"></a>xTaskPriorityInherit</STRONG> (Thumb, 154 bytes, Stack size 16 bytes, tasks.o(i.xTaskPriorityInherit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTaskPriorityInherit
</UL>
<BR>[Calls]<UL><LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
</UL>

<P><STRONG><a name="[2b0]"></a>xTaskRemoveFromEventList</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, tasks.o(i.xTaskRemoveFromEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGiveFromISR
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[280]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceiveFromISR
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[29d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
</UL>

<P><STRONG><a name="[2aa]"></a>xTaskResumeAll</STRONG> (Thumb, 226 bytes, Stack size 16 bytes, tasks.o(i.xTaskResumeAll))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
<LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupSetBits
<LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vEventGroupDelete
<LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
</UL>

<P><STRONG><a name="[287]"></a>xTimerCreate</STRONG> (Thumb, 54 bytes, Stack size 40 bytes, timers.o(i.xTimerCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = xTimerCreate &rArr; prvInitialiseNewTimer &rArr; prvCheckForValidListAndQueue &rArr; xQueueGenericCreateStatic &rArr; prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[2a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTimer
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_timer_create
</UL>

<P><STRONG><a name="[2cc]"></a>xTimerCreateTimerTask</STRONG> (Thumb, 100 bytes, Stack size 32 bytes, timers.o(i.xTimerCreateTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = xTimerCreateTimerTask &rArr; xTaskCreateStatic &rArr; prvInitialiseNewTask
</UL>
<BR>[Calls]<UL><LI><a href="#[2da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationGetTimerTaskMemory
<LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>
<BR>[Called By]<UL><LI><a href="#[278]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[288]"></a>xTimerGenericCommand</STRONG> (Thumb, 128 bytes, Stack size 48 bytes, timers.o(i.xTimerGenericCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = xTimerGenericCommand &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
</UL>
<BR>[Called By]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_timer_stop_fromisr
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_timer_start_fromisr
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;os_timer_delete
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[ab]"></a>ad7606_busy_irq_callback_fun</STRONG> (Thumb, 114 bytes, Stack size 24 bytes, ad7606_driver.o(i.ad7606_busy_irq_callback_fun))
<BR><BR>[Stack]<UL><LI>Max Depth = 432<LI>Call Chain = ad7606_busy_irq_callback_fun &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ad7606_driver.o(i.ad7606_init)
</UL>
<P><STRONG><a name="[ae]"></a>ad7606_deinit</STRONG> (Thumb, 128 bytes, Stack size 32 bytes, ad7606_driver.o(i.ad7606_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = ad7606_deinit &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ad7606_driver.o(i.bsp_ad7606_driver_instance)
</UL>
<P><STRONG><a name="[ad]"></a>ad7606_init</STRONG> (Thumb, 210 bytes, Stack size 40 bytes, ad7606_driver.o(i.ad7606_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = ad7606_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ad7606_driver.o(i.bsp_ad7606_driver_instance)
</UL>
<P><STRONG><a name="[b0]"></a>ad7606_read_busy</STRONG> (Thumb, 84 bytes, Stack size 32 bytes, ad7606_driver.o(i.ad7606_read_busy))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = ad7606_read_busy &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ad7606_driver.o(i.bsp_ad7606_driver_instance)
</UL>
<P><STRONG><a name="[b2]"></a>ad7606_read_data</STRONG> (Thumb, 270 bytes, Stack size 72 bytes, ad7606_driver.o(i.ad7606_read_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = ad7606_read_data &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ad7606_driver.o(i.bsp_ad7606_driver_instance)
</UL>
<P><STRONG><a name="[b1]"></a>ad7606_reset</STRONG> (Thumb, 150 bytes, Stack size 32 bytes, ad7606_driver.o(i.ad7606_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = ad7606_reset &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ad7606_driver.o(i.bsp_ad7606_driver_instance)
</UL>
<P><STRONG><a name="[b3]"></a>ad7606_set_sampling_mode</STRONG> (Thumb, 378 bytes, Stack size 40 bytes, ad7606_driver.o(i.ad7606_set_sampling_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = ad7606_set_sampling_mode &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ad7606_driver.o(i.bsp_ad7606_driver_instance)
</UL>
<P><STRONG><a name="[b4]"></a>ad7606_set_sampling_range</STRONG> (Thumb, 162 bytes, Stack size 40 bytes, ad7606_driver.o(i.ad7606_set_sampling_range))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = ad7606_set_sampling_range &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ad7606_driver.o(i.bsp_ad7606_driver_instance)
</UL>
<P><STRONG><a name="[aa]"></a>ad7606_spi_rx_dma_callback_fun</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, ad7606_driver.o(i.ad7606_spi_rx_dma_callback_fun))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = ad7606_spi_rx_dma_callback_fun &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ad7606_driver.o(i.ad7606_init)
</UL>
<P><STRONG><a name="[af]"></a>ad7606_startconvst</STRONG> (Thumb, 140 bytes, Stack size 32 bytes, ad7606_driver.o(i.ad7606_startconvst))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = ad7606_startconvst &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ad7606_driver.o(i.bsp_ad7606_driver_instance)
</UL>
<P><STRONG><a name="[b7]"></a>__adc_collect_event_thread_notify</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, bsp_adc_collect_xxx_handler.o(i.__adc_collect_event_thread_notify))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = __adc_collect_event_thread_notify &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp_adc_collect_xxx_handler.o(i.bsp_adc_collect_xxx_handler_init)
</UL>
<P><STRONG><a name="[1ed]"></a>_mount_handler</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, bsp_adc_collect_xxx_handler.o(i._mount_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = _mount_handler &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_collect_event_thread
</UL>

<P><STRONG><a name="[b6]"></a>adc_collect_irq_callback</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, bsp_adc_collect_xxx_handler.o(i.adc_collect_irq_callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = adc_collect_irq_callback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> bsp_adc_collect_xxx_handler.o(i.bsp_adc_collect_xxx_handler_init)
</UL>
<P><STRONG><a name="[20d]"></a>bsp_adc_collect_xxx_handler_init</STRONG> (Thumb, 628 bytes, Stack size 40 bytes, bsp_adc_collect_xxx_handler.o(i.bsp_adc_collect_xxx_handler_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = bsp_adc_collect_xxx_handler_init &rArr; ring_buff_instance &rArr; ring_buff_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ring_buff_instance
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_ad7606_driver_instance
</UL>
<BR>[Called By]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_collect_xxx_handler_inst
</UL>

<P><STRONG><a name="[1fe]"></a>bsp_adc_collect_xxx_handler_inst</STRONG> (Thumb, 558 bytes, Stack size 40 bytes, bsp_adc_collect_xxx_handler.o(i.bsp_adc_collect_xxx_handler_inst))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = bsp_adc_collect_xxx_handler_inst &rArr; bsp_adc_collect_xxx_handler_init &rArr; ring_buff_instance &rArr; ring_buff_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_collect_xxx_handler_init
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_collect_event_thread
</UL>

<P><STRONG><a name="[201]"></a>process_adc_collect_xxx_event</STRONG> (Thumb, 328 bytes, Stack size 40 bytes, bsp_adc_collect_xxx_handler.o(i.process_adc_collect_xxx_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = process_adc_collect_xxx_event &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_collect_set_event_thread
</UL>

<P><STRONG><a name="[211]"></a>_allocate_led_state</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, bsp_led_handler.o(i._allocate_led_state))
<BR><BR>[Called By]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_handler_register_driver
</UL>

<P><STRONG><a name="[1e7]"></a>_find_led_state_by_which</STRONG> (Thumb, 80 bytes, Stack size 32 bytes, bsp_led_handler.o(i._find_led_state_by_which))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _find_led_state_by_which
</UL>
<BR>[Calls]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetNextNode
</UL>
<BR>[Called By]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_handler_unregister_driver
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_handler_register_driver
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_led_handler_control_internal
</UL>

<P><STRONG><a name="[214]"></a>_free_led_state</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, bsp_led_handler.o(i._free_led_state))
<BR><BR>[Called By]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_handler_unregister_driver
</UL>

<P><STRONG><a name="[1ea]"></a>_get_blink_off_time</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, bsp_led_handler.o(i._get_blink_off_time))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _get_blink_off_time
</UL>
<BR>[Calls]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_blink_on_time
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_process_led_blink
</UL>

<P><STRONG><a name="[1eb]"></a>_get_blink_on_time</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, bsp_led_handler.o(i._get_blink_on_time))
<BR><BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_process_led_blink
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_blink_off_time
</UL>

<P><STRONG><a name="[1ec]"></a>_led_handler_control_internal</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, bsp_led_handler.o(i._led_handler_control_internal))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _led_handler_control_internal &rArr; _find_led_state_by_which
</UL>
<BR>[Calls]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_find_led_state_by_which
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_handler_task
</UL>

<P><STRONG><a name="[1f5]"></a>_notify_led_state_change</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, bsp_led_handler.o(i._notify_led_state_change))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _notify_led_state_change
</UL>
<BR>[Called By]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_process_led_timer
</UL>

<P><STRONG><a name="[1f3]"></a>_process_led_blink</STRONG> (Thumb, 148 bytes, Stack size 24 bytes, bsp_led_handler.o(i._process_led_blink))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _process_led_blink &rArr; _get_blink_off_time
</UL>
<BR>[Calls]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_blink_on_time
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_blink_off_time
</UL>
<BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_update_led_state
</UL>

<P><STRONG><a name="[1f9]"></a>_process_led_breath</STRONG> (Thumb, 176 bytes, Stack size 24 bytes, bsp_led_handler.o(i._process_led_breath))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _process_led_breath
</UL>
<BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_update_led_state
</UL>

<P><STRONG><a name="[1f4]"></a>_process_led_timer</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, bsp_led_handler.o(i._process_led_timer))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _process_led_timer &rArr; _notify_led_state_change
</UL>
<BR>[Calls]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_notify_led_state_change
</UL>
<BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_update_led_state
</UL>

<P><STRONG><a name="[1f8]"></a>_update_led_state</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, bsp_led_handler.o(i._update_led_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _update_led_state &rArr; _process_led_blink &rArr; _get_blink_off_time
</UL>
<BR>[Calls]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_process_led_timer
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_process_led_breath
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_process_led_blink
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_handler_task
</UL>

<P><STRONG><a name="[1ee]"></a>_notify_comm_status_change</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, uart_led_indicator.o(i._notify_comm_status_change))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = _notify_comm_status_change &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_notify_state_change
</UL>

<P><STRONG><a name="[1ef]"></a>_notify_state_change</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uart_led_indicator.o(i._notify_state_change))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = _notify_state_change &rArr; _notify_comm_status_change &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_notify_comm_status_change
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_led_indicator_on_led_state_changed
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_led_indicator_on_data_received
</UL>

<P><STRONG><a name="[1f6]"></a>_start_led_indication</STRONG> (Thumb, 212 bytes, Stack size 64 bytes, uart_led_indicator.o(i._start_led_indication))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = _start_led_indication &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_handler_control
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_led_indicator_on_data_received
</UL>

<P><STRONG><a name="[1ce]"></a>__flash_check_value</STRONG> (Thumb, 226 bytes, Stack size 24 bytes, flash_event_fun.o(i.__flash_check_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = __flash_check_value &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc32
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_save_sys_info
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_read_sys_info
</UL>

<P><STRONG><a name="[c5]"></a>handle_read_sys_info</STRONG> (Thumb, 124 bytes, Stack size 40 bytes, flash_event_fun.o(i.handle_read_sys_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = handle_read_sys_info &rArr; __flash_check_value &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__flash_check_value
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> flash_event_fun.o(i.flash_event_register_cb_init)
</UL>
<P><STRONG><a name="[c4]"></a>handle_save_sys_info</STRONG> (Thumb, 208 bytes, Stack size 80 bytes, flash_event_fun.o(i.handle_save_sys_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = handle_save_sys_info &rArr; __flash_check_value &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc32
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__flash_check_value
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> flash_event_fun.o(i.flash_event_register_cb_init)
</UL>
<P><STRONG><a name="[c3]"></a>flash_event_handler_thread</STRONG> (Thumb, 102 bytes, Stack size 32 bytes, flash_event_handler.o(i.flash_event_handler_thread))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = flash_event_handler_thread &rArr; process_flash_event &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[25e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_flash_event
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> flash_event_handler.o(i.flash_event_handler_init)
</UL>
<P><STRONG><a name="[25e]"></a>process_flash_event</STRONG> (Thumb, 172 bytes, Stack size 40 bytes, flash_event_handler.o(i.process_flash_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = process_flash_event &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_event_handler_thread
</UL>

<P><STRONG><a name="[1a2]"></a>modbus_wait_errordata_recv</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, modbus_rtu.o(i.modbus_wait_errordata_recv))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = modbus_wait_errordata_recv
</UL>
<BR>[Called By]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_Reply
</UL>

<P><STRONG><a name="[cf]"></a>__read_reg_data_callbackfun</STRONG> (Thumb, 44 bytes, Stack size 32 bytes, reg_adress_msg.o(i.__read_reg_data_callbackfun))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = __read_reg_data_callbackfun &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> reg_adress_msg.o(i.modbus_read_reg)
</UL>
<P><STRONG><a name="[3]"></a>Rs485DeviceInit</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, rs485_dev.o(i.Rs485DeviceInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Rs485DeviceInit &rArr; ADDUARTDEVICE
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADDUARTDEVICE
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rs485_dev.o(.data)
</UL>
<P><STRONG><a name="[5]"></a>Rs485_Recv</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, rs485_dev.o(i.Rs485_Recv))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Rs485_Recv
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rs485_dev.o(.data)
</UL>
<P><STRONG><a name="[4]"></a>Rs485_Send</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, rs485_dev.o(i.Rs485_Send))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Rs485_Send
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rs485_dev.o(.data)
</UL>
<P><STRONG><a name="[1]"></a>Select_UartDev</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, rs485_dev.o(i.Select_UartDev))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Select_UartDev &rArr; get_uart_dev &rArr; strcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_uart_dev
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rs485_dev.o(.data)
</UL>
<P><STRONG><a name="[2]"></a>SetRs485DeviceMode</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, rs485_dev.o(i.SetRs485DeviceMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SetRs485DeviceMode
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rs485_dev.o(.data)
</UL>
<P><STRONG><a name="[28b]"></a>process_rs485_event</STRONG> (Thumb, 156 bytes, Stack size 40 bytes, rs485_event_handler.o(i.process_rs485_event))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = process_rs485_event &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handle_thread
</UL>

<P><STRONG><a name="[dc]"></a>rs485_event_handle_thread</STRONG> (Thumb, 152 bytes, Stack size 56 bytes, rs485_event_handler.o(i.rs485_event_handle_thread))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = rs485_event_handle_thread &rArr; rs485_reg_value_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_reg_value_init
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_rs485_event
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rs485_event_handler.o(i.rs485_event_handler_init)
</UL>
<P><STRONG><a name="[2bc]"></a>rs485_get_reg_place</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, rs485_event_handler.o(i.rs485_get_reg_place))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = rs485_get_reg_place &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_handle_write_reg_data
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_handle_read_reg_data
</UL>

<P><STRONG><a name="[d9]"></a>rs485_handle_collect_data_input</STRONG> (Thumb, 130 bytes, Stack size 32 bytes, rs485_event_handler.o(i.rs485_handle_collect_data_input))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = rs485_handle_collect_data_input &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rs485_event_handler.o(i.rs485_event_fun_init)
</UL>
<P><STRONG><a name="[da]"></a>rs485_handle_read_reg_data</STRONG> (Thumb, 210 bytes, Stack size 40 bytes, rs485_event_handler.o(i.rs485_handle_read_reg_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = rs485_handle_read_reg_data &rArr; rs485_get_reg_place &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_get_reg_place
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rs485_event_handler.o(i.rs485_event_fun_init)
</UL>
<P><STRONG><a name="[db]"></a>rs485_handle_write_reg_data</STRONG> (Thumb, 264 bytes, Stack size 40 bytes, rs485_event_handler.o(i.rs485_handle_write_reg_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = rs485_handle_write_reg_data &rArr; rs485_process_reg_value &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_process_reg_value
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_get_reg_place
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rs485_event_handler.o(i.rs485_event_fun_init)
</UL>
<P><STRONG><a name="[dd]"></a>rs485_output_msg_thread</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, rs485_event_handler.o(i.rs485_output_msg_thread))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = rs485_output_msg_thread &rArr; Modbus_rtu_Reply &rArr; Modbus_rtu_cmd_0x50_Reply &rArr; __merge_reg_data &rArr; modbus_read_reg &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_rtu_Reply
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Modbus_RTU_cmd_Recv
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rs485_event_handler.o(i.rs485_event_handler_init)
</UL>
<P><STRONG><a name="[2bd]"></a>rs485_process_reg_value</STRONG> (Thumb, 150 bytes, Stack size 32 bytes, rs485_event_handler.o(i.rs485_process_reg_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = rs485_process_reg_value &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_handle_write_reg_data
</UL>

<P><STRONG><a name="[2b5]"></a>rs485_reg_value_init</STRONG> (Thumb, 86 bytes, Stack size 40 bytes, rs485_event_handler.o(i.rs485_reg_value_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = rs485_reg_value_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_event_handle_thread
</UL>

<P><STRONG><a name="[6]"></a>uart_init</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, uart_dev.o(i.uart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = uart_init &rArr; xQueueCreateMutex &rArr; prvInitialiseMutex &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateMutex
<LI><a href="#[2c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_uarthandle
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uart_dev.o(.data)
</UL>
<P><STRONG><a name="[8]"></a>uart_recv</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, uart_dev.o(i.uart_recv))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = uart_recv &rArr; xQueueReceive &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uart_dev.o(.data)
</UL>
<P><STRONG><a name="[7]"></a>uart_send</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, uart_dev.o(i.uart_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = uart_send &rArr; xQueueSemaphoreTake &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uart_dev.o(.data)
</UL>
<P><STRONG><a name="[144]"></a>__NVIC_GetPriorityGrouping</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[145]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[12f]"></a>DMA_CalcBaseAndBitshift</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_DeInit
</UL>

<P><STRONG><a name="[131]"></a>DMA_CheckFifoParam</STRONG> (Thumb, 170 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[133]"></a>DMA_SetConfig</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, stm32f4xx_hal_dma.o(i.DMA_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[123]"></a>FLASH_Program_Byte</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f4xx_hal_flash.o(i.FLASH_Program_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FLASH_Program_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Program
</UL>

<P><STRONG><a name="[124]"></a>FLASH_Program_DoubleWord</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FLASH_Program_DoubleWord
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Program
</UL>

<P><STRONG><a name="[125]"></a>FLASH_Program_HalfWord</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FLASH_Program_HalfWord
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Program
</UL>

<P><STRONG><a name="[126]"></a>FLASH_Program_Word</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_hal_flash.o(i.FLASH_Program_Word))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FLASH_Program_Word
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Program
</UL>

<P><STRONG><a name="[128]"></a>FLASH_SetErrorCode</STRONG> (Thumb, 172 bytes, Stack size 0 bytes, stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode))
<BR><BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>

<P><STRONG><a name="[122]"></a>FLASH_MassErase</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FLASH_MassErase
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_Erase
</UL>

<P><STRONG><a name="[9c]"></a>SPI_2linesRxISR_16BIT</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = SPI_2linesRxISR_16BIT &rArr; SPI_CloseRxTx_ISR &rArr; HAL_SPI_RxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT)
</UL>
<P><STRONG><a name="[9e]"></a>SPI_2linesRxISR_8BIT</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = SPI_2linesRxISR_8BIT &rArr; SPI_CloseRxTx_ISR &rArr; HAL_SPI_RxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT)
</UL>
<P><STRONG><a name="[9d]"></a>SPI_2linesTxISR_16BIT</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = SPI_2linesTxISR_16BIT &rArr; SPI_CloseRxTx_ISR &rArr; HAL_SPI_RxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT)
</UL>
<P><STRONG><a name="[9f]"></a>SPI_2linesTxISR_8BIT</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = SPI_2linesTxISR_8BIT &rArr; SPI_CloseRxTx_ISR &rArr; HAL_SPI_RxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT)
</UL>
<P><STRONG><a name="[1b6]"></a>SPI_CloseRxTx_ISR</STRONG> (Thumb, 182 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = SPI_CloseRxTx_ISR &rArr; HAL_SPI_RxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxRxCpltCallback
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_RxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_2linesTxISR_8BIT
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_2linesTxISR_16BIT
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_2linesRxISR_8BIT
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_2linesRxISR_16BIT
</UL>

<P><STRONG><a name="[1b8]"></a>SPI_CloseRx_ISR</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 432<LI>Call Chain = SPI_CloseRx_ISR &rArr; HAL_SPI_RxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTransaction
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_RxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_RxISR_8BIT
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_RxISR_16BIT
</UL>

<P><STRONG><a name="[1b9]"></a>SPI_CloseTx_ISR</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = SPI_CloseTx_ISR &rArr; HAL_SPI_TxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_TxISR_8BIT
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_TxISR_16BIT
</UL>

<P><STRONG><a name="[94]"></a>SPI_DMAAbortOnError</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler)
</UL>
<P><STRONG><a name="[97]"></a>SPI_DMAError</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.SPI_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
</UL>
<BR>[Address Reference Count : 3]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA)
<LI> stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA)
<LI> stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA)
</UL>
<P><STRONG><a name="[95]"></a>SPI_DMAHalfReceiveCplt</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_DMAHalfReceiveCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_RxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 2]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA)
<LI> stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA)
</UL>
<P><STRONG><a name="[a0]"></a>SPI_DMAHalfTransmitCplt</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_DMAHalfTransmitCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA)
</UL>
<P><STRONG><a name="[9a]"></a>SPI_DMAHalfTransmitReceiveCplt</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_DMAHalfTransmitReceiveCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxRxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA)
</UL>
<P><STRONG><a name="[96]"></a>SPI_DMAReceiveCplt</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 432<LI>Call Chain = SPI_DMAReceiveCplt &rArr; HAL_SPI_RxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTransaction
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_RxCpltCallback
</UL>
<BR>[Address Reference Count : 2]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA)
<LI> stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA)
</UL>
<P><STRONG><a name="[a1]"></a>SPI_DMATransmitCplt</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = SPI_DMATransmitCplt &rArr; HAL_SPI_TxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA)
</UL>
<P><STRONG><a name="[9b]"></a>SPI_DMATransmitReceiveCplt</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SPI_DMATransmitReceiveCplt &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxRxCpltCallback
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA)
</UL>
<P><STRONG><a name="[15d]"></a>SPI_EndRxTransaction</STRONG> (Thumb, 148 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SPI_EndRxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAReceiveCplt
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRx_ISR
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Receive
</UL>

<P><STRONG><a name="[162]"></a>SPI_EndRxTxTransaction</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitReceiveCplt
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMATransmitCplt
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseTx_ISR
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRxTx_ISR
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Transmit
</UL>

<P><STRONG><a name="[98]"></a>SPI_RxISR_16BIT</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = SPI_RxISR_16BIT &rArr; SPI_CloseRx_ISR &rArr; HAL_SPI_RxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT)
</UL>
<P><STRONG><a name="[99]"></a>SPI_RxISR_8BIT</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = SPI_RxISR_8BIT &rArr; SPI_CloseRx_ISR &rArr; HAL_SPI_RxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseRx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT)
</UL>
<P><STRONG><a name="[a2]"></a>SPI_TxISR_16BIT</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = SPI_TxISR_16BIT &rArr; SPI_CloseTx_ISR &rArr; HAL_SPI_TxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseTx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT)
</UL>
<P><STRONG><a name="[a3]"></a>SPI_TxISR_8BIT</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = SPI_TxISR_8BIT &rArr; SPI_CloseTx_ISR &rArr; HAL_SPI_TxCpltCallback &rArr; elog_printf &rArr; SEGGER_RTT_printf &rArr; SEGGER_RTT_vprintf &rArr; _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseTx_ISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT)
</UL>
<P><STRONG><a name="[1bd]"></a>SPI_WaitFlagStateUntilTimeout</STRONG> (Thumb, 212 bytes, Stack size 40 bytes, stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTransaction
</UL>

<P><STRONG><a name="[17b]"></a>TIM_SlaveTimer_SetConfig</STRONG> (Thumb, 756 bytes, Stack size 32 bytes, stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = TIM_SlaveTimer_SetConfig &rArr; TIM_TI2_ConfigInputStage
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI2_ConfigInputStage
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI1_ConfigInputStage
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETR_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_SlaveConfigSynchro
</UL>

<P><STRONG><a name="[1c4]"></a>TIM_TI1_ConfigInputStage</STRONG> (Thumb, 38 bytes, Stack size 12 bytes, stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_TI1_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SlaveTimer_SetConfig
</UL>

<P><STRONG><a name="[1c5]"></a>TIM_TI2_ConfigInputStage</STRONG> (Thumb, 40 bytes, Stack size 12 bytes, stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_TI2_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SlaveTimer_SetConfig
</UL>

<P><STRONG><a name="[16d]"></a>TIM_TI2_SetConfig</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_TI2_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_ConfigChannel
</UL>

<P><STRONG><a name="[16e]"></a>TIM_TI3_SetConfig</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_TI3_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_ConfigChannel
</UL>

<P><STRONG><a name="[16f]"></a>TIM_TI4_SetConfig</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_TI4_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_ConfigChannel
</UL>

<P><STRONG><a name="[a4]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = UART_DMAAbortOnError &rArr; HAL_UART_ErrorCallback &rArr; HAL_UART_Receive_IT &rArr; UART_Start_Receive_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[183]"></a>UART_EndRxTransfer</STRONG> (Thumb, 108 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_EndRxTransfer))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>

<P><STRONG><a name="[185]"></a>UART_EndTransmit_IT</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = UART_EndTransmit_IT &rArr; HAL_UART_TxCpltCallback &rArr; xQueueGiveFromISR &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[182]"></a>UART_Receive_IT</STRONG> (Thumb, 252 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(i.UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; uart_led_indicator_on_data_received &rArr; _start_led_indication &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[188]"></a>UART_SetConfig</STRONG> (Thumb, 664 bytes, Stack size 48 bytes, stm32f4xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[184]"></a>UART_Transmit_IT</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_Transmit_IT))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[189]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 140 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive
</UL>

<P><STRONG><a name="[267]"></a>GetSector</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, mcu_flash_driver.o(i.GetSector))
<BR><BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;internal_flash_write
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;internal_flash_erase
</UL>

<P><STRONG><a name="[269]"></a>GetSectorSize</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, mcu_flash_driver.o(i.GetSectorSize))
<BR><BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;internal_flash_erase
</UL>

<P><STRONG><a name="[9]"></a>internal_flash_init</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, mcu_flash_driver.o(i.internal_flash_init))
<BR>[Address Reference Count : 1]<UL><LI> mcu_flash_driver.o(.data)
</UL>
<P><STRONG><a name="[c7]"></a>gpio_deinit</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, mcu_gpio_driver.o(i.gpio_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = gpio_deinit &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_gpio_driver.o(i.gpio_dev_instance)
</UL>
<P><STRONG><a name="[c6]"></a>gpio_init</STRONG> (Thumb, 80 bytes, Stack size 32 bytes, mcu_gpio_driver.o(i.gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = gpio_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_gpio_driver.o(i.gpio_dev_instance)
</UL>
<P><STRONG><a name="[1cd]"></a>__find_spi_dev_for_timner</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, mcu_spi_driver.o(i.__find_spi_dev_for_timner))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = __find_spi_dev_for_timner &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dev_timer_callback
</UL>

<P><STRONG><a name="[e2]"></a>spi_dev_deinit</STRONG> (Thumb, 124 bytes, Stack size 32 bytes, mcu_spi_driver.o(i.spi_dev_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = spi_dev_deinit &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_spi_driver.o(i.spi_dev_instance)
</UL>
<P><STRONG><a name="[e1]"></a>spi_dev_init</STRONG> (Thumb, 324 bytes, Stack size 32 bytes, mcu_spi_driver.o(i.spi_dev_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = spi_dev_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_spi_driver.o(i.spi_dev_instance)
</UL>
<P><STRONG><a name="[e4]"></a>spi_dev_read</STRONG> (Thumb, 198 bytes, Stack size 48 bytes, mcu_spi_driver.o(i.spi_dev_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = spi_dev_read &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_spi_driver.o(i.spi_dev_instance)
</UL>
<P><STRONG><a name="[e0]"></a>spi_dev_timer_callback</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, mcu_spi_driver.o(i.spi_dev_timer_callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = spi_dev_timer_callback &rArr; __find_spi_dev_for_timner &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__find_spi_dev_for_timner
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_spi_driver.o(i.spi_dev_init)
</UL>
<P><STRONG><a name="[e3]"></a>spi_dev_write</STRONG> (Thumb, 176 bytes, Stack size 48 bytes, mcu_spi_driver.o(i.spi_dev_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = spi_dev_write &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_spi_driver.o(i.spi_dev_instance)
</UL>
<P><STRONG><a name="[df]"></a>spi_interrupt_rxcallbackfun</STRONG> (Thumb, 114 bytes, Stack size 32 bytes, mcu_spi_driver.o(i.spi_interrupt_rxcallbackfun))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = spi_interrupt_rxcallbackfun &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__find_spi_dev_private_data
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_spi_driver.o(i.spi_dev_init)
</UL>
<P><STRONG><a name="[de]"></a>spi_interrupt_txcallbackfun</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, mcu_spi_driver.o(i.spi_interrupt_txcallbackfun))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = spi_interrupt_txcallbackfun
</UL>
<BR>[Calls]<UL><LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__find_spi_dev_private_data
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mcu_spi_driver.o(i.spi_dev_init)
</UL>
<P><STRONG><a name="[cc]"></a>lpf_fir</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, lpf_fir_alogorithm.o(i.lpf_fir))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = lpf_fir &rArr; arm_fir_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_fir_f32
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lpf_fir_alogorithm.o(i.lpf_fir_instance)
</UL>
<P><STRONG><a name="[cb]"></a>lpf_fir_init</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, lpf_fir_alogorithm.o(i.lpf_fir_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = lpf_fir_init &rArr; arm_fir_init_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_fir_init_f32
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_biquad_cascade_df1_init_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lpf_fir_instance
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lpf_fir_alogorithm.o(i.lpf_fir_instance)
</UL>
<P><STRONG><a name="[cd]"></a>lpf_iir</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, lpf_fir_alogorithm.o(i.lpf_iir))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = lpf_iir &rArr; arm_biquad_cascade_df1_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_biquad_cascade_df1_f32
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lpf_fir_alogorithm.o(i.lpf_fir_instance)
</UL>
<P><STRONG><a name="[277]"></a>__get_IPSR</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, cmsis_os2.o(i.__get_IPSR))
<BR><BR>[Called By]<UL><LI><a href="#[272]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osKernelStart
<LI><a href="#[271]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osKernelInitialize
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osThreadNew
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
</UL>

<P><STRONG><a name="[2cf]"></a>prvTestWaitCondition</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, event_groups.o(i.prvTestWaitCondition))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvTestWaitCondition
</UL>
<BR>[Called By]<UL><LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
</UL>

<P><STRONG><a name="[2b2]"></a>prvHeapInit</STRONG> (Thumb, 98 bytes, Stack size 12 bytes, heap_4.o(i.prvHeapInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = prvHeapInit
</UL>
<BR>[Called By]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[2b3]"></a>prvInsertBlockIntoFreeList</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, heap_4.o(i.prvInsertBlockIntoFreeList))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvInsertBlockIntoFreeList
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[d0]"></a>prvTaskExitError</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, port.o(i.prvTaskExitError))
<BR>[Address Reference Count : 1]<UL><LI> port.o(i.pxPortInitialiseStack)
</UL>
<P><STRONG><a name="[298]"></a>prvCopyDataFromQueue</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, queue.o(i.prvCopyDataFromQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = prvCopyDataFromQueue &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[280]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceiveFromISR
</UL>

<P><STRONG><a name="[299]"></a>prvCopyDataToQueue</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, queue.o(i.prvCopyDataToQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = prvCopyDataToQueue &rArr; xTaskPriorityDisinherit
</UL>
<BR>[Calls]<UL><LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[2d9]"></a>prvGetDisinheritPriorityAfterTimeout</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, queue.o(i.prvGetDisinheritPriorityAfterTimeout))
<BR><BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
</UL>

<P><STRONG><a name="[29b]"></a>prvInitialiseMutex</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, queue.o(i.prvInitialiseMutex))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = prvInitialiseMutex &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>
<BR>[Called By]<UL><LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateMutex
</UL>

<P><STRONG><a name="[29c]"></a>prvInitialiseNewQueue</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, queue.o(i.prvInitialiseNewQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[29d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
</UL>
<BR>[Called By]<UL><LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreateStatic
</UL>

<P><STRONG><a name="[2a3]"></a>prvIsQueueEmpty</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, queue.o(i.prvIsQueueEmpty))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prvIsQueueEmpty
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[2a4]"></a>prvIsQueueFull</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, queue.o(i.prvIsQueueFull))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prvIsQueueFull
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[2af]"></a>prvUnlockQueue</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, queue.o(i.prvUnlockQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskMissedYield
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>

<P><STRONG><a name="[28c]"></a>prvAddCurrentTaskToDelayedList</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, tasks.o(i.prvAddCurrentTaskToDelayedList))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskNotifyTake
<LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[2c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnUnorderedEventList
</UL>

<P><STRONG><a name="[290]"></a>prvAddNewTaskToReadyList</STRONG> (Thumb, 156 bytes, Stack size 8 bytes, tasks.o(i.prvAddNewTaskToReadyList))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prvAddNewTaskToReadyList &rArr; prvInitialiseTaskLists
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[291]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseTaskLists
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
</UL>
<BR>[Called By]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
</UL>

<P><STRONG><a name="[296]"></a>prvCheckTasksWaitingTermination</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, tasks.o(i.prvCheckTasksWaitingTermination))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = prvCheckTasksWaitingTermination &rArr; prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[297]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDeleteTCB
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
</UL>

<P><STRONG><a name="[297]"></a>prvDeleteTCB</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, tasks.o(i.prvDeleteTCB))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
</UL>
<BR>[Called By]<UL><LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[296]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
</UL>

<P><STRONG><a name="[ed]"></a>prvIdleTask</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, tasks.o(i.prvIdleTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = prvIdleTask &rArr; prvCheckTasksWaitingTermination &rArr; prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[296]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
</UL>
<BR>[Address Reference Count : 1]<UL><LI> tasks.o(i.vTaskStartScheduler)
</UL>
<P><STRONG><a name="[29e]"></a>prvInitialiseNewTask</STRONG> (Thumb, 200 bytes, Stack size 40 bytes, tasks.o(i.prvInitialiseNewTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = prvInitialiseNewTask
</UL>
<BR>[Calls]<UL><LI><a href="#[2a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxPortInitialiseStack
<LI><a href="#[29f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialiseItem
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
</UL>

<P><STRONG><a name="[291]"></a>prvInitialiseTaskLists</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, tasks.o(i.prvInitialiseTaskLists))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvInitialiseTaskLists
</UL>
<BR>[Calls]<UL><LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>

<P><STRONG><a name="[2c7]"></a>prvResetNextTaskUnblockTime</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, tasks.o(i.prvResetNextTaskUnblockTime))
<BR><BR>[Called By]<UL><LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
</UL>

<P><STRONG><a name="[292]"></a>prvCheckForValidListAndQueue</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, timers.o(i.prvCheckForValidListAndQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = prvCheckForValidListAndQueue &rArr; xQueueGenericCreateStatic &rArr; prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreateStatic
<LI><a href="#[295]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueAddToRegistry
</UL>
<BR>[Called By]<UL><LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[2a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTimer
</UL>

<P><STRONG><a name="[2ae]"></a>prvGetNextExpireTime</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, timers.o(i.prvGetNextExpireTime))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[2a1]"></a>prvInitialiseNewTimer</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, timers.o(i.prvInitialiseNewTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = prvInitialiseNewTimer &rArr; prvCheckForValidListAndQueue &rArr; xQueueGenericCreateStatic &rArr; prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[29f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialiseItem
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>
<BR>[Called By]<UL><LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreate
</UL>

<P><STRONG><a name="[2a2]"></a>prvInsertTimerInActiveList</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, timers.o(i.prvInsertTimerInActiveList))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvInsertTimerInActiveList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>

<P><STRONG><a name="[2a5]"></a>prvProcessExpiredTimer</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, timers.o(i.prvProcessExpiredTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = prvProcessExpiredTimer &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
</UL>
<BR>[Called By]<UL><LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
</UL>

<P><STRONG><a name="[2a6]"></a>prvProcessReceivedCommands</STRONG> (Thumb, 350 bytes, Stack size 48 bytes, timers.o(i.prvProcessReceivedCommands))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = prvProcessReceivedCommands &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[2a8]"></a>prvProcessTimerOrBlockTask</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, timers.o(i.prvProcessTimerOrBlockTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = prvProcessTimerOrBlockTask &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[2a7]"></a>prvSampleTimeNow</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, timers.o(i.prvSampleTimeNow))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
</UL>
<BR>[Called By]<UL><LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>

<P><STRONG><a name="[2ad]"></a>prvSwitchTimerLists</STRONG> (Thumb, 154 bytes, Stack size 32 bytes, timers.o(i.prvSwitchTimerLists))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
</UL>

<P><STRONG><a name="[ee]"></a>prvTimerTask</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, timers.o(i.prvTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = prvTimerTask &rArr; prvProcessReceivedCommands &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetNextExpireTime
</UL>
<BR>[Address Reference Count : 1]<UL><LI> timers.o(i.xTimerCreateTimerTask)
</UL>
<P><STRONG><a name="[21b]"></a>disassembly_ins_is_bl_blx</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cm_backtrace.o(i.disassembly_ins_is_bl_blx))
<BR><BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_call_stack
</UL>

<P><STRONG><a name="[21f]"></a>dump_stack</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, cm_backtrace.o(i.dump_stack))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = dump_stack &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
</UL>

<P><STRONG><a name="[220]"></a>fault_diagnosis</STRONG> (Thumb, 764 bytes, Stack size 8 bytes, cm_backtrace.o(i.fault_diagnosis))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = fault_diagnosis &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
</UL>

<P><STRONG><a name="[21d]"></a>get_cur_thread_name</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, cm_backtrace.o(i.get_cur_thread_name))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_cur_thread_name
</UL>
<BR>[Calls]<UL><LI><a href="#[261]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskName
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
</UL>

<P><STRONG><a name="[218]"></a>get_cur_thread_stack_info</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, cm_backtrace.o(i.get_cur_thread_stack_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = get_cur_thread_stack_info &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStackSize
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStackAddr
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_call_stack
</UL>

<P><STRONG><a name="[221]"></a>print_call_stack</STRONG> (Thumb, 118 bytes, Stack size 152 bytes, cm_backtrace.o(i.print_call_stack))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = print_call_stack &rArr; cm_backtrace_call_stack &rArr; get_cur_thread_stack_info &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_call_stack
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
</UL>

<P><STRONG><a name="[21e]"></a>statck_del_fpu_regs</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, cm_backtrace.o(i.statck_del_fpu_regs))
<BR><BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
</UL>

<P><STRONG><a name="[1ac]"></a>_DoInit</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, SEGGER_RTT.o(i._DoInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _DoInit
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_Write
</UL>

<P><STRONG><a name="[1ae]"></a>_GetAvailWriteSpace</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, SEGGER_RTT.o(i._GetAvailWriteSpace))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _GetAvailWriteSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
</UL>

<P><STRONG><a name="[1b0]"></a>_WriteBlocking</STRONG> (Thumb, 118 bytes, Stack size 40 bytes, SEGGER_RTT.o(i._WriteBlocking))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
</UL>

<P><STRONG><a name="[1af]"></a>_WriteNoCheck</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, SEGGER_RTT.o(i._WriteNoCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _WriteNoCheck &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
</UL>

<P><STRONG><a name="[1b4]"></a>_PrintInt</STRONG> (Thumb, 236 bytes, Stack size 48 bytes, SEGGER_RTT_printf.o(i._PrintInt))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = _PrintInt &rArr; _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_StoreChar
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_PrintUnsigned
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_vprintf
</UL>

<P><STRONG><a name="[1b5]"></a>_PrintUnsigned</STRONG> (Thumb, 230 bytes, Stack size 48 bytes, SEGGER_RTT_printf.o(i._PrintUnsigned))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = _PrintUnsigned &rArr; _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_StoreChar
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_vprintf
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_PrintInt
</UL>

<P><STRONG><a name="[1b3]"></a>_StoreChar</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, SEGGER_RTT_printf.o(i._StoreChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = _StoreChar &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteBlocking &rArr; __aeabi_memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_vprintf
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_PrintUnsigned
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_PrintInt
</UL>

<P><STRONG><a name="[1fa]"></a>ad7606_gpio_source_inst</STRONG> (Thumb, 336 bytes, Stack size 32 bytes, system_adaption.o(i.ad7606_gpio_source_inst))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = ad7606_gpio_source_inst &rArr; gpio_dev_instance &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_dev_instance
</UL>
<BR>[Called By]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_collect_handler_source_inst
</UL>

<P><STRONG><a name="[1ff]"></a>adc_collect_handler_source_inst</STRONG> (Thumb, 154 bytes, Stack size 16 bytes, system_adaption.o(i.adc_collect_handler_source_inst))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = adc_collect_handler_source_inst &rArr; ad7606_spi_dev_source_inst &rArr; spi_dev_instance &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_spi_dev_source_inst
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad7606_gpio_source_inst
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_source_inst
</UL>

<P><STRONG><a name="[22d]"></a>data_process_task_instance</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, system_adaption.o(i.data_process_task_instance))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = data_process_task_instance &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_source_inst
</UL>

<P><STRONG><a name="[250]"></a>fft_calculate_task_instance</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, system_adaption.o(i.fft_calculate_task_instance))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = fft_calculate_task_instance &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lpf_fir_instance
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_source_inst
</UL>

<P><STRONG><a name="[26b]"></a>led_driver_inst</STRONG> (Thumb, 132 bytes, Stack size 48 bytes, system_adaption.o(i.led_driver_inst))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = led_driver_inst &rArr; bsp_led_driver_inst &rArr; bsp_led_driver_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_driver_inst
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_gpio_interface_inst
</UL>
<BR>[Called By]<UL><LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_handler_inst
</UL>

<P><STRONG><a name="[26c]"></a>led_gpio_interface_inst</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, system_adaption.o(i.led_gpio_interface_inst))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = led_gpio_interface_inst &rArr; gpio_dev_instance &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_dev_instance
</UL>
<BR>[Called By]<UL><LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_driver_inst
</UL>

<P><STRONG><a name="[26d]"></a>led_handler_inst</STRONG> (Thumb, 288 bytes, Stack size 48 bytes, system_adaption.o(i.led_handler_inst))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = led_handler_inst &rArr; led_driver_inst &rArr; bsp_led_driver_inst &rArr; bsp_led_driver_init &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_handler_register_driver
<LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_handler_instance
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_handler_control
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_driver_inst
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_source_inst
</UL>

<P><STRONG><a name="[2e]"></a>mcu_spi_cs_high</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, system_adaption.o(i.mcu_spi_cs_high))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = mcu_spi_cs_high &rArr; HAL_GPIO_WritePin
</UL>
<BR>[Calls]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[2f]"></a>mcu_spi_cs_low</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, system_adaption.o(i.mcu_spi_cs_low))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = mcu_spi_cs_low &rArr; HAL_GPIO_WritePin
</UL>
<BR>[Calls]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[30]"></a>spi_dev_init</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, system_adaption.o(i.spi_dev_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = spi_dev_init &rArr; MX_SPI2_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_NVIC_SetPriority &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> system_adaption.o(.data)
</UL>
<P><STRONG><a name="[be]"></a>handle_voltage_data</STRONG> (Thumb, 190 bytes, Stack size 72 bytes, data_proc_calcu_fun.o(i.handle_voltage_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = handle_voltage_data &rArr; __process_amp_result_data &rArr; calculate_median_average &rArr; MidFilterBlock &rArr; arm_sort_f32 &rArr; arm_bitonic_sort_f32 &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__process_amp_result_data
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> data_proc_calcu_fun.o(i.data_proc_register_cb_init)
</UL>
<P><STRONG><a name="[228]"></a>validate_function_pointers</STRONG> (Thumb, 196 bytes, Stack size 16 bytes, data_process_task.o(i.validate_function_pointers))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = validate_function_pointers &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;data_proc_handler_init
</UL>

<P><STRONG><a name="[24e]"></a>fft_calcuate_3rd</STRONG> (Thumb, 128 bytes, Stack size 40 bytes, fft_calculate_task.o(i.fft_calcuate_3rd))
<BR><BR>[Stack]<UL><LI>Max Depth = 528<LI>Call Chain = fft_calcuate_3rd &rArr; fft_calculate_window &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
</UL>
<BR>[Called By]<UL><LI><a href="#[256]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync_fft_calculate
</UL>

<P><STRONG><a name="[256]"></a>sync_fft_calculate</STRONG> (Thumb, 1602 bytes, Stack size 80 bytes, fft_calculate_task.o(i.sync_fft_calculate))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = sync_fft_calculate &rArr; fft_calcuate_3rd &rArr; fft_calculate_window &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calculate_window
<LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_calcuate_3rd
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_event_handler_thread
</UL>

<P><STRONG><a name="[255]"></a>sync_fft_check_ready</STRONG> (Thumb, 464 bytes, Stack size 64 bytes, fft_calculate_task.o(i.sync_fft_check_ready))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = sync_fft_check_ready &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_collection_data_thread
</UL>

<P><STRONG><a name="[258]"></a>sync_fft_reset_status</STRONG> (Thumb, 78 bytes, Stack size 24 bytes, fft_calculate_task.o(i.sync_fft_reset_status))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = sync_fft_reset_status &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_event_handler_thread
</UL>

<P><STRONG><a name="[257]"></a>sync_fft_send_result</STRONG> (Thumb, 418 bytes, Stack size 48 bytes, fft_calculate_task.o(i.sync_fft_send_result))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = sync_fft_send_result &rArr; elog_output &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fft_event_handler_thread
</UL>

<P><STRONG><a name="[24a]"></a>safe_mutex_give</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, elog_port_improved.o(i.safe_mutex_give))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = safe_mutex_give &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[282]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetCurrentTaskHandle
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>
<BR>[Called By]<UL><LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_output_unlock
</UL>

<P><STRONG><a name="[249]"></a>safe_mutex_take</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, elog_port_improved.o(i.safe_mutex_take))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = safe_mutex_take &rArr; xQueueSemaphoreTake &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[282]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetCurrentTaskHandle
</UL>
<BR>[Called By]<UL><LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_port_output_lock
</UL>

<P><STRONG><a name="[239]"></a>elog_set_filter_tag_lvl_default</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, elog.o(i.elog_set_filter_tag_lvl_default))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = elog_set_filter_tag_lvl_default
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_init
</UL>

<P><STRONG><a name="[23d]"></a>get_fmt_enabled</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, elog.o(i.get_fmt_enabled))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + In Cycle
<LI>Call Chain = get_fmt_enabled &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fmt_used_and_enabled_u32
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fmt_used_and_enabled_ptr
</UL>

<P><STRONG><a name="[241]"></a>get_fmt_used_and_enabled_ptr</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, elog.o(i.get_fmt_used_and_enabled_ptr))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = get_fmt_used_and_enabled_ptr &rArr; get_fmt_enabled &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fmt_enabled
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>

<P><STRONG><a name="[242]"></a>get_fmt_used_and_enabled_u32</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, elog.o(i.get_fmt_used_and_enabled_u32))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = get_fmt_used_and_enabled_u32 &rArr; get_fmt_enabled &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fmt_enabled
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_output
</UL>

<P><STRONG><a name="[202]"></a>async_get_buf_space</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, elog_async.o(i.async_get_buf_space))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = async_get_buf_space
</UL>
<BR>[Calls]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_async_get_buf_used
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;async_put_log
</UL>

<P><STRONG><a name="[c0]"></a>async_output</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, elog_async.o(i.async_output))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = async_output &rArr; async_output_elog &rArr; elog_output_unlock &rArr; elog_port_output_unlock &rArr; safe_mutex_give &rArr; xQueueGenericSend &rArr; vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;async_output_elog
</UL>
<BR>[Address Reference Count : 1]<UL><LI> elog_async.o(i.elog_async_init)
</UL>
<P><STRONG><a name="[20a]"></a>async_put_log</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, elog_async.o(i.async_put_log))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = async_put_log &rArr; elog_set_output_enabled &rArr;  elog_output (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;async_get_buf_space
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_set_output_enabled
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elog_async_output
</UL>

<P><STRONG><a name="[203]"></a>elog_async_get_buf_used</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, elog_async.o(i.elog_async_get_buf_used))
<BR><BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;async_output_elog
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;async_get_buf_space
</UL>

<P><STRONG><a name="[115]"></a>arm_heapify</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, arm_heap_sort_f32.o(.text.arm_heapify))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = arm_heapify
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_heap_sort_f32
</UL>

<P><STRONG><a name="[117]"></a>arm_quick_sort_core_f32</STRONG> (Thumb, 202 bytes, Stack size 32 bytes, arm_quick_sort_f32.o(.text.arm_quick_sort_core_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + In Cycle
<LI>Call Chain = arm_quick_sort_core_f32 &rArr;  arm_quick_sort_core_f32 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_quick_sort_core_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_quick_sort_f32
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_quick_sort_core_f32
</UL>

<P><STRONG><a name="[1e9]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>
<BR>[Called By]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1c8]"></a>_printf_core</STRONG> (Thumb, 1744 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsnprintf
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0snprintf
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[1f1]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1f0]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[a7]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 2]<UL><LI> printfa.o(i.__0vsnprintf)
<LI> printfa.o(i.__0snprintf)
</UL>
<P><STRONG><a name="[a8]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
