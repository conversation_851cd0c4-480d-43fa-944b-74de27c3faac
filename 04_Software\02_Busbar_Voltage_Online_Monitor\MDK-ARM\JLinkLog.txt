T7398 000:013.796   SEGGER J-Link V7.96t Log File
T7398 000:013.932   DLL Compiled: Jul 11 2024 09:13:33
T7398 000:013.936   Logging started @ 2025-08-02 08:48
T7398 000:013.940   Process: C:\Keil_v5\UV4\UV4.exe
T7398 000:013.946 - 13.943ms
T7398 000:013.953 JLINK_SetWarnOutHandler(...)
T7398 000:013.956 - 0.004ms
T7398 000:013.960 JLINK_OpenEx(...)
T7398 000:016.548   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T7398 000:017.776   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T7398 000:017.901   Decompressing FW timestamp took 101 us
T7398 000:028.457   Hardware: V9.60
T7398 000:028.470   S/N: 69658975
T7398 000:028.474   OEM: SEGGER
T7398 000:028.479   Feature(s): R<PERSON>, G<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JFlash
T7398 000:029.393   Bootloader: (Could not read)
T7398 000:032.578   TELNET listener socket opened on port 19021
T7398 000:032.780   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T7398 000:032.902   WEBSRV Webserver running on local port 19080
T7398 000:033.027   Looking for J-Link GUI Server exe at: C:\Keil_v5\ARM\Segger\JLinkGUIServer.exe
T7398 000:033.148   Looking for J-Link GUI Server exe at: C:\Program Files\SEGGER\JLink_V796t\JLinkGUIServer.exe
T7398 000:033.192   Forking J-Link GUI Server: C:\Program Files\SEGGER\JLink_V796t\JLinkGUIServer.exe
T7398 000:054.353   J-Link GUI Server info: "J-Link GUI server V7.96t "
T7398 000:058.263 - 44.298ms returns "O.K."
T7398 000:058.290 JLINK_GetEmuCaps()
T7398 000:058.296 - 0.004ms returns 0xB9FF7BBF
T7398 000:058.302 JLINK_TIF_GetAvailable(...)
T7398 000:058.981 - 0.679ms
T7398 000:058.999 JLINK_SetErrorOutHandler(...)
T7398 000:059.003 - 0.003ms
T7398 000:059.292 JLINK_ExecCommand("ProjectFile = "D:\arrester_insulation_monitoring\04_Software\02_Busbar_Voltage_Online_Monitor\MDK-ARM\JLinkSettings.ini"", ...). 
T7398 000:079.304   Ref file found at: C:\Keil_v5\ARM\Segger\JLinkDevices.ref
T7398 000:079.431   REF file references invalid XML file: C:\Program Files\SEGGER\JLink_V796t\JLinkDevices.xml
T7398 000:080.444 - 21.152ms returns 0x00
T7398 000:080.492 JLINK_ExecCommand("Device = STM32F411RETx", ...). 
T7398 000:082.073   Device "STM32F411RE" selected.
T7398 000:082.325 - 1.826ms returns 0x00
T7398 000:082.336 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T7398 000:082.342   ERROR: Unknown command
T7398 000:082.346 - 0.006ms returns 0x01
T7398 000:082.353 JLINK_GetHardwareVersion()
T7398 000:082.359 - 0.006ms returns 96000
T7398 000:082.363 JLINK_GetDLLVersion()
T7398 000:082.366 - 0.003ms returns 79620
T7398 000:082.369 JLINK_GetOEMString(...)
T7398 000:082.374 JLINK_GetFirmwareString(...)
T7398 000:082.377 - 0.003ms
T7398 000:082.397 JLINK_GetDLLVersion()
T7398 000:082.400 - 0.003ms returns 79620
T7398 000:082.403 JLINK_GetCompileDateTime()
T7398 000:082.406 - 0.003ms
T7398 000:082.413 JLINK_GetFirmwareString(...)
T7398 000:082.417 - 0.003ms
T7398 000:082.422 JLINK_GetHardwareVersion()
T7398 000:082.425 - 0.003ms returns 96000
T7398 000:082.431 JLINK_GetSN()
T7398 000:082.434 - 0.003ms returns 69658975
T7398 000:082.440 JLINK_GetOEMString(...)
T7398 000:082.448 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T7398 000:084.577 - 2.128ms returns 0x00
T7398 000:084.600 JLINK_HasError()
T7398 000:084.617 JLINK_SetSpeed(5000)
T7398 000:084.691 - 0.075ms
T7398 000:084.708 JLINK_GetId()
T7398 000:084.915   InitTarget() start
T7398 000:084.928    J-Link Script File: Executing InitTarget()
T7398 000:085.147   SWD selected. Executing JTAG -> SWD switching sequence.
T7398 000:087.520   DAP initialized successfully.
T7398 000:098.140   InitTarget() end - Took 13.1ms
T7398 000:100.129   Found SW-DP with ID 0x2BA01477
T7398 000:104.090   DPIDR: 0x2BA01477
T7398 000:104.108   CoreSight SoC-400 or earlier
T7398 000:104.116   Scanning AP map to find all available APs
T7398 000:105.001   AP[1]: Stopped AP scan as end of AP map has been reached
T7398 000:105.026   AP[0]: AHB-AP (IDR: 0x24770011)
T7398 000:105.033   Iterating through AP map to find AHB-AP to use
T7398 000:107.009   AP[0]: Core found
T7398 000:107.024   AP[0]: AHB-AP ROM base: 0xE00FF000
T7398 000:107.506   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T7398 000:107.524   Found Cortex-M4 r0p1, Little endian.
T7398 000:108.148   -- Max. mem block: 0x00010AA0
T7398 000:109.034   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7398 000:109.569   CPU_ReadMem(4 bytes @ 0x********)
T7398 000:110.577   FPUnit: 6 code (BP) slots and 2 literal slots
T7398 000:110.588   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T7398 000:110.986   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7398 000:111.555   CPU_ReadMem(4 bytes @ 0xE0001000)
T7398 000:111.978   CPU_WriteMem(4 bytes @ 0xE0001000)
T7398 000:112.397   CPU_ReadMem(4 bytes @ 0xE000ED88)
T7398 000:112.981   CPU_WriteMem(4 bytes @ 0xE000ED88)
T7398 000:113.397   CPU_ReadMem(4 bytes @ 0xE000ED88)
T7398 000:114.559   CPU_WriteMem(4 bytes @ 0xE000ED88)
T7398 000:114.986   CoreSight components:
T7398 000:114.994   ROMTbl[0] @ E00FF000
T7398 000:115.000   CPU_ReadMem(64 bytes @ 0xE00FF000)
T7398 000:116.090   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T7398 000:117.010   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
T7398 000:117.017   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T7398 000:117.423   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
T7398 000:117.428   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T7398 000:117.999   [0][2]: ******** CID B105E00D PID 002BB003 FPB
T7398 000:118.004   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T7398 000:119.010   [0][3]: ******** CID B105E00D PID 003BB001 ITM
T7398 000:119.022   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T7398 000:119.430   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
T7398 000:119.442   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T7398 000:120.430   [0][5]: ******** CID B105900D PID 000BB925 ETM
T7398 000:121.115 - 36.406ms returns 0x2BA01477
T7398 000:121.133 JLINK_GetDLLVersion()
T7398 000:121.137 - 0.003ms returns 79620
T7398 000:121.143 JLINK_CORE_GetFound()
T7398 000:121.146 - 0.003ms returns 0xE0000FF
T7398 000:121.183 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T7398 000:121.198   Value=0xE00FF000
T7398 000:121.203 - 0.019ms returns 0
T7398 000:121.216 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T7398 000:121.220   Value=0xE00FF000
T7398 000:121.224 - 0.007ms returns 0
T7398 000:121.228 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T7398 000:121.231   Value=0x********
T7398 000:121.235 - 0.007ms returns 0
T7398 000:121.247 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T7398 000:121.426   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T7398 000:122.005   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T7398 000:122.013 - 0.766ms returns 32 (0x20)
T7398 000:122.019 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T7398 000:122.022   Value=0x00000000
T7398 000:122.027 - 0.008ms returns 0
T7398 000:122.036 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T7398 000:122.040   Value=0x********
T7398 000:122.044 - 0.007ms returns 0
T7398 000:122.047 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T7398 000:122.050   Value=0x********
T7398 000:122.054 - 0.007ms returns 0
T7398 000:122.058 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T7398 000:122.061   Value=0xE0001000
T7398 000:122.065 - 0.007ms returns 0
T7398 000:122.069 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T7398 000:122.072   Value=0x********
T7398 000:122.076 - 0.007ms returns 0
T7398 000:122.080 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T7398 000:122.083   Value=0xE000E000
T7398 000:122.087 - 0.007ms returns 0
T7398 000:122.091 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T7398 000:122.093   Value=0xE000EDF0
T7398 000:122.098 - 0.007ms returns 0
T7398 000:122.101 JLINK_GetDebugInfo(0x01 = Unknown)
T7398 000:122.104   Value=0x00000001
T7398 000:122.108 - 0.007ms returns 0
T7398 000:122.112 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T7398 000:122.119   CPU_ReadMem(4 bytes @ 0xE000ED00)
T7398 000:122.399   Data:  41 C2 0F 41
T7398 000:122.404   Debug reg: CPUID
T7398 000:122.412 - 0.299ms returns 1 (0x1)
T7398 000:122.416 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T7398 000:122.420   Value=0x00000000
T7398 000:122.424 - 0.007ms returns 0
T7398 000:122.428 JLINK_HasError()
T7398 000:122.432 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T7398 000:122.436 - 0.003ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T7398 000:122.439 JLINK_Reset()
T7398 000:122.449   CPU is running
T7398 000:122.454   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T7398 000:123.009   CPU is running
T7398 000:123.015   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7398 000:123.558   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T7398 000:124.407   Reset: Reset device via AIRCR.SYSRESETREQ.
T7398 000:124.417   CPU is running
T7398 000:124.422   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T7398 000:179.679   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7398 000:179.979   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7398 000:183.544   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7398 000:189.874   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7398 000:193.534   CPU_WriteMem(4 bytes @ 0x********)
T7398 000:193.979   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T7398 000:194.400   CPU_ReadMem(4 bytes @ 0xE0001000)
T7398 000:195.555 - 73.115ms
T7398 000:195.603 JLINK_HasError()
T7398 000:195.839 JLINK_ReadReg(R15 (PC))
T7398 000:195.853 - 0.241ms returns 0x0800027C
T7398 000:195.858 JLINK_ReadReg(XPSR)
T7398 000:195.868 - 0.009ms returns 0x01000000
T7398 000:195.873 JLINK_Halt()
T7398 000:195.876 - 0.003ms returns 0x00
T7398 000:195.880 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T7398 000:195.888   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7398 000:196.404   Data:  03 00 03 00
T7398 000:196.413   Debug reg: DHCSR
T7398 000:196.418 - 0.538ms returns 1 (0x1)
T7398 000:196.424 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T7398 000:196.427   Debug reg: DHCSR
T7398 000:196.688   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T7398 000:197.401 - 0.976ms returns 0 (0x00000000)
T7398 000:197.407 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T7398 000:197.411   Debug reg: DEMCR
T7398 000:197.419   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7398 000:197.975 - 0.567ms returns 0 (0x00000000)
T7398 000:198.004 JLINK_GetHWStatus(...)
T7398 000:198.399 - 0.394ms returns 0
T7398 000:198.425 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T7398 000:198.429 - 0.004ms returns 0x06
T7398 000:198.433 JLINK_GetNumBPUnits(Type = 0xF0)
T7398 000:198.436 - 0.003ms returns 0x2000
T7398 000:198.440 JLINK_GetNumWPUnits()
T7398 000:198.443 - 0.003ms returns 4
T7398 000:198.452 JLINK_GetSpeed()
T7398 000:198.455 - 0.003ms returns 4000
T7398 000:198.462 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T7398 000:198.468   CPU_ReadMem(4 bytes @ 0xE000E004)
T7398 000:198.984   Data:  02 00 00 00
T7398 000:198.995 - 0.533ms returns 1 (0x1)
T7398 000:199.001 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T7398 000:199.008   CPU_ReadMem(4 bytes @ 0xE000E004)
T7398 000:199.403   Data:  02 00 00 00
T7398 000:199.411 - 0.410ms returns 1 (0x1)
T7398 000:199.417 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T7398 000:199.420   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T7398 000:199.430   CPU_WriteMem(28 bytes @ 0xE0001000)
T7398 000:200.406 - 0.989ms returns 0x1C
T7398 000:200.421 JLINK_HasError()
T7398 000:200.426 JLINK_ReadReg(R15 (PC))
T7398 000:200.432 - 0.005ms returns 0x0800027C
T7398 000:200.436 JLINK_ReadReg(XPSR)
T7398 000:200.439 - 0.003ms returns 0x01000000
T7398 000:201.985 JLINK_ReadMemEx(0xE0001004, 0x4 Bytes, Flags = 0x02000000)
T7398 000:201.999   Data:  00 00 00 00
T7398 000:202.005   Debug reg: DWT_CYCCNT
T7398 000:202.009 - 0.024ms returns 4 (0x4)
T7398 000:295.578 JLINK_HasError()
T7398 000:295.603 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T7398 000:295.608 - 0.004ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T7398 000:295.612 JLINK_Reset()
T7398 000:295.624   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T7398 000:295.984   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7398 000:296.570   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T7398 000:298.411   Reset: Reset device via AIRCR.SYSRESETREQ.
T7398 000:298.428   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T7398 000:352.985   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7398 000:353.560   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7398 000:353.980   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7398 000:360.871   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7398 000:363.640   CPU_WriteMem(4 bytes @ 0x********)
T7398 000:364.557   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T7398 000:364.979   CPU_ReadMem(4 bytes @ 0xE0001000)
T7398 000:365.402 - 69.789ms
T7398 000:365.458 JLINK_HasError()
T7398 000:365.463 JLINK_ReadReg(R15 (PC))
T7398 000:365.469 - 0.005ms returns 0x0800027C
T7398 000:365.472 JLINK_ReadReg(XPSR)
T7398 000:365.475 - 0.003ms returns 0x01000000
T7398 000:365.831 JLINK_ReadMemEx(0x08000198, 0x3C Bytes, Flags = 0x02000000)
T7398 000:365.843   CPU_ReadMem(128 bytes @ 0x08000180)
T7398 000:366.773    -- Updating C cache (128 bytes @ 0x08000180)
T7398 000:366.780    -- Read from C cache (60 bytes @ 0x08000198)
T7398 000:366.786   Data:  DF F8 0C D0 00 F0 70 FC 00 48 00 47 41 5C 01 08 ...
T7398 000:366.791 - 0.959ms returns 60 (0x3C)
T7398 000:366.796 JLINK_ReadMemEx(0x08000198, 0x2 Bytes, Flags = 0x02000000)
T7398 000:366.800    -- Read from C cache (2 bytes @ 0x08000198)
T7398 000:366.804   Data:  DF F8
T7398 000:366.809 - 0.012ms returns 2 (0x2)
T7398 000:366.869 JLINK_ReadMemEx(0x0800019A, 0x2 Bytes, Flags = 0x02000000)
T7398 000:366.872    -- Read from C cache (2 bytes @ 0x0800019A)
T7398 000:366.876   Data:  0C D0
T7398 000:366.881 - 0.012ms returns 2 (0x2)
T7398 000:366.891 JLINK_ReadMemEx(0x0800019C, 0x3C Bytes, Flags = 0x02000000)
T7398 000:366.894    -- Read from C cache (60 bytes @ 0x0800019C)
T7398 000:366.899   Data:  00 F0 70 FC 00 48 00 47 41 5C 01 08 40 6C 01 20 ...
T7398 000:366.903 - 0.012ms returns 60 (0x3C)
T7398 000:366.907 JLINK_ReadMemEx(0x0800019C, 0x2 Bytes, Flags = 0x02000000)
T7398 000:366.910    -- Read from C cache (2 bytes @ 0x0800019C)
T7398 000:366.915   Data:  00 F0
T7398 000:366.919 - 0.012ms returns 2 (0x2)
T7398 000:366.923 JLINK_ReadMemEx(0x0800019E, 0x2 Bytes, Flags = 0x02000000)
T7398 000:366.926    -- Read from C cache (2 bytes @ 0x0800019E)
T7398 000:366.930   Data:  70 FC
T7398 000:366.935 - 0.011ms returns 2 (0x2)
T7398 000:366.942 JLINK_ReadMemEx(0x080001A0, 0x3C Bytes, Flags = 0x02000000)
T7398 000:366.945    -- Read from C cache (60 bytes @ 0x080001A0)
T7398 000:366.950   Data:  00 48 00 47 41 5C 01 08 40 6C 01 20 2C 4B 19 68 ...
T7398 000:366.954 - 0.012ms returns 60 (0x3C)
T7398 000:366.958 JLINK_ReadMemEx(0x080001A0, 0x2 Bytes, Flags = 0x02000000)
T7398 000:366.961    -- Read from C cache (2 bytes @ 0x080001A0)
T7398 000:366.972   Data:  00 48
T7398 000:366.976 - 0.018ms returns 2 (0x2)
T7398 000:366.980 JLINK_ReadMemEx(0x080001A2, 0x2 Bytes, Flags = 0x02000000)
T7398 000:366.983    -- Read from C cache (2 bytes @ 0x080001A2)
T7398 000:366.987   Data:  00 47
T7398 000:366.992 - 0.011ms returns 2 (0x2)
T7398 000:366.996 JLINK_ReadMemEx(0x080001A2, 0x2 Bytes, Flags = 0x02000000)
T7398 000:366.999    -- Read from C cache (2 bytes @ 0x080001A2)
T7398 000:367.004   Data:  00 47
T7398 000:367.008 - 0.012ms returns 2 (0x2)
T7398 000:367.012 JLINK_ReadMemEx(0x080001A4, 0x3C Bytes, Flags = 0x02000000)
T7398 000:367.015    -- Read from C cache (60 bytes @ 0x080001A4)
T7398 000:367.019   Data:  41 5C 01 08 40 6C 01 20 2C 4B 19 68 08 68 B0 E8 ...
T7398 000:367.024 - 0.012ms returns 60 (0x3C)
T7398 000:367.027 JLINK_ReadMemEx(0x080001A4, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.030    -- Read from C cache (2 bytes @ 0x080001A4)
T7398 000:367.035   Data:  41 5C
T7398 000:367.039 - 0.011ms returns 2 (0x2)
T7398 000:367.043 JLINK_ReadMemEx(0x080001AC, 0x3C Bytes, Flags = 0x02000000)
T7398 000:367.046    -- Read from C cache (60 bytes @ 0x080001AC)
T7398 000:367.051   Data:  2C 4B 19 68 08 68 B0 E8 F0 4F 80 F3 09 88 BF F3 ...
T7398 000:367.055 - 0.012ms returns 60 (0x3C)
T7398 000:367.058 JLINK_ReadMemEx(0x080001AC, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.096    -- Read from C cache (2 bytes @ 0x080001AC)
T7398 000:367.101   Data:  2C 4B
T7398 000:367.106 - 0.047ms returns 2 (0x2)
T7398 000:367.109 JLINK_ReadMemEx(0x080001AE, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.112    -- Read from C cache (2 bytes @ 0x080001AE)
T7398 000:367.117   Data:  19 68
T7398 000:367.121 - 0.011ms returns 2 (0x2)
T7398 000:367.125 JLINK_ReadMemEx(0x080001AE, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.128    -- Read from C cache (2 bytes @ 0x080001AE)
T7398 000:367.132   Data:  19 68
T7398 000:367.136 - 0.011ms returns 2 (0x2)
T7398 000:367.140 JLINK_ReadMemEx(0x080001B0, 0x3C Bytes, Flags = 0x02000000)
T7398 000:367.143    -- Read from C cache (60 bytes @ 0x080001B0)
T7398 000:367.148   Data:  08 68 B0 E8 F0 4F 80 F3 09 88 BF F3 6F 8F 4F F0 ...
T7398 000:367.152 - 0.012ms returns 60 (0x3C)
T7398 000:367.156 JLINK_ReadMemEx(0x080001B0, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.159    -- Read from C cache (2 bytes @ 0x080001B0)
T7398 000:367.163   Data:  08 68
T7398 000:367.167 - 0.011ms returns 2 (0x2)
T7398 000:367.171 JLINK_ReadMemEx(0x080001B0, 0x3C Bytes, Flags = 0x02000000)
T7398 000:367.174    -- Read from C cache (60 bytes @ 0x080001B0)
T7398 000:367.179   Data:  08 68 B0 E8 F0 4F 80 F3 09 88 BF F3 6F 8F 4F F0 ...
T7398 000:367.183 - 0.012ms returns 60 (0x3C)
T7398 000:367.187 JLINK_ReadMemEx(0x080001B0, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.190    -- Read from C cache (2 bytes @ 0x080001B0)
T7398 000:367.194   Data:  08 68
T7398 000:367.198 - 0.011ms returns 2 (0x2)
T7398 000:367.202 JLINK_ReadMemEx(0x080001B2, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.205    -- Read from C cache (2 bytes @ 0x080001B2)
T7398 000:367.209   Data:  B0 E8
T7398 000:367.213 - 0.011ms returns 2 (0x2)
T7398 000:367.217 JLINK_ReadMemEx(0x080001B2, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.220    -- Read from C cache (2 bytes @ 0x080001B2)
T7398 000:367.224   Data:  B0 E8
T7398 000:367.229 - 0.011ms returns 2 (0x2)
T7398 000:367.232 JLINK_ReadMemEx(0x080001B4, 0x3C Bytes, Flags = 0x02000000)
T7398 000:367.235    -- Read from C cache (60 bytes @ 0x080001B4)
T7398 000:367.240   Data:  F0 4F 80 F3 09 88 BF F3 6F 8F 4F F0 00 00 80 F3 ...
T7398 000:367.244 - 0.012ms returns 60 (0x3C)
T7398 000:367.248 JLINK_ReadMemEx(0x080001B4, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.251    -- Read from C cache (2 bytes @ 0x080001B4)
T7398 000:367.255   Data:  F0 4F
T7398 000:367.259 - 0.011ms returns 2 (0x2)
T7398 000:367.263 JLINK_ReadMemEx(0x080001B6, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.266    -- Read from C cache (2 bytes @ 0x080001B6)
T7398 000:367.271   Data:  80 F3
T7398 000:367.275 - 0.012ms returns 2 (0x2)
T7398 000:367.279 JLINK_ReadMemEx(0x080001B8, 0x3C Bytes, Flags = 0x02000000)
T7398 000:367.282    -- Read from C cache (60 bytes @ 0x080001B8)
T7398 000:367.286   Data:  09 88 BF F3 6F 8F 4F F0 00 00 80 F3 11 88 70 47 ...
T7398 000:367.291 - 0.011ms returns 60 (0x3C)
T7398 000:367.294 JLINK_ReadMemEx(0x080001B8, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.297    -- Read from C cache (2 bytes @ 0x080001B8)
T7398 000:367.302   Data:  09 88
T7398 000:367.306 - 0.011ms returns 2 (0x2)
T7398 000:367.310 JLINK_ReadMemEx(0x080001BA, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.313    -- Read from C cache (2 bytes @ 0x080001BA)
T7398 000:367.317   Data:  BF F3
T7398 000:367.321 - 0.011ms returns 2 (0x2)
T7398 000:367.325 JLINK_ReadMemEx(0x080001BC, 0x3C Bytes, Flags = 0x02000000)
T7398 000:367.328    -- Read from C cache (60 bytes @ 0x080001BC)
T7398 000:367.332   Data:  6F 8F 4F F0 00 00 80 F3 11 88 70 47 00 00 00 00 ...
T7398 000:367.337 - 0.011ms returns 60 (0x3C)
T7398 000:367.340 JLINK_ReadMemEx(0x080001BC, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.343    -- Read from C cache (2 bytes @ 0x080001BC)
T7398 000:367.348   Data:  6F 8F
T7398 000:367.352 - 0.011ms returns 2 (0x2)
T7398 000:367.356 JLINK_ReadMemEx(0x080001BE, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.359    -- Read from C cache (2 bytes @ 0x080001BE)
T7398 000:367.364   Data:  4F F0
T7398 000:367.369 - 0.013ms returns 2 (0x2)
T7398 000:367.373 JLINK_ReadMemEx(0x080001C0, 0x3C Bytes, Flags = 0x02000000)
T7398 000:367.376    -- Read from C cache (60 bytes @ 0x080001C0)
T7398 000:367.380   Data:  00 00 80 F3 11 88 70 47 00 00 00 00 08 48 00 68 ...
T7398 000:367.384 - 0.012ms returns 60 (0x3C)
T7398 000:367.388 JLINK_ReadMemEx(0x080001C0, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.391    -- Read from C cache (2 bytes @ 0x080001C0)
T7398 000:367.395   Data:  00 00
T7398 000:367.399 - 0.011ms returns 2 (0x2)
T7398 000:367.403 JLINK_ReadMemEx(0x080001C2, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.406    -- Read from C cache (2 bytes @ 0x080001C2)
T7398 000:367.410   Data:  80 F3
T7398 000:367.415 - 0.011ms returns 2 (0x2)
T7398 000:367.419 JLINK_ReadMemEx(0x080001C4, 0x3C Bytes, Flags = 0x02000000)
T7398 000:367.422    -- Read from C cache (60 bytes @ 0x080001C4)
T7398 000:367.426   Data:  11 88 70 47 00 00 00 00 08 48 00 68 00 68 80 F3 ...
T7398 000:367.431 - 0.012ms returns 60 (0x3C)
T7398 000:367.434 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.437    -- Read from C cache (2 bytes @ 0x080001C4)
T7398 000:367.442   Data:  11 88
T7398 000:367.446 - 0.011ms returns 2 (0x2)
T7398 000:367.450 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T7398 000:367.453    -- Read from C cache (2 bytes @ 0x080001C6)
T7398 000:367.457   Data:  70 47
T7398 000:367.461 - 0.011ms returns 2 (0x2)
T7398 000:367.465 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T7398 000:367.469   CPU_ReadMem(64 bytes @ 0x08000200)
T7398 000:368.054    -- Updating C cache (64 bytes @ 0x08000200)
T7398 000:368.060    -- Read from C cache (60 bytes @ 0x080001C8)
T7398 000:368.064   Data:  00 00 00 00 08 48 00 68 00 68 80 F3 08 88 4F F0 ...
T7398 000:368.069 - 0.604ms returns 60 (0x3C)
T7398 000:368.073 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.076    -- Read from C cache (2 bytes @ 0x080001C8)
T7398 000:368.080   Data:  00 00
T7398 000:368.085 - 0.011ms returns 2 (0x2)
T7398 000:368.089 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.093    -- Read from C cache (60 bytes @ 0x080001CC)
T7398 000:368.097   Data:  08 48 00 68 00 68 80 F3 08 88 4F F0 00 00 80 F3 ...
T7398 000:368.101 - 0.012ms returns 60 (0x3C)
T7398 000:368.105 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.108    -- Read from C cache (2 bytes @ 0x080001CC)
T7398 000:368.112   Data:  08 48
T7398 000:368.117 - 0.011ms returns 2 (0x2)
T7398 000:368.120 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.123    -- Read from C cache (2 bytes @ 0x080001CE)
T7398 000:368.128   Data:  00 68
T7398 000:368.132 - 0.011ms returns 2 (0x2)
T7398 000:368.136 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.139    -- Read from C cache (2 bytes @ 0x080001CE)
T7398 000:368.143   Data:  00 68
T7398 000:368.147 - 0.011ms returns 2 (0x2)
T7398 000:368.151 JLINK_ReadMemEx(0x080001D0, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.154    -- Read from C cache (60 bytes @ 0x080001D0)
T7398 000:368.158   Data:  00 68 80 F3 08 88 4F F0 00 00 80 F3 14 88 62 B6 ...
T7398 000:368.163 - 0.012ms returns 60 (0x3C)
T7398 000:368.166 JLINK_ReadMemEx(0x080001D0, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.170    -- Read from C cache (2 bytes @ 0x080001D0)
T7398 000:368.174   Data:  00 68
T7398 000:368.178 - 0.011ms returns 2 (0x2)
T7398 000:368.182 JLINK_ReadMemEx(0x080001D0, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.185    -- Read from C cache (60 bytes @ 0x080001D0)
T7398 000:368.189   Data:  00 68 80 F3 08 88 4F F0 00 00 80 F3 14 88 62 B6 ...
T7398 000:368.194 - 0.012ms returns 60 (0x3C)
T7398 000:368.197 JLINK_ReadMemEx(0x080001D0, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.200    -- Read from C cache (2 bytes @ 0x080001D0)
T7398 000:368.204   Data:  00 68
T7398 000:368.209 - 0.011ms returns 2 (0x2)
T7398 000:368.214 JLINK_ReadMemEx(0x080001D2, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.217    -- Read from C cache (2 bytes @ 0x080001D2)
T7398 000:368.222   Data:  80 F3
T7398 000:368.226 - 0.011ms returns 2 (0x2)
T7398 000:368.230 JLINK_ReadMemEx(0x080001D2, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.233    -- Read from C cache (2 bytes @ 0x080001D2)
T7398 000:368.237   Data:  80 F3
T7398 000:368.241 - 0.011ms returns 2 (0x2)
T7398 000:368.245 JLINK_ReadMemEx(0x080001D4, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.248    -- Read from C cache (60 bytes @ 0x080001D4)
T7398 000:368.252   Data:  08 88 4F F0 00 00 80 F3 14 88 62 B6 61 B6 BF F3 ...
T7398 000:368.257 - 0.012ms returns 60 (0x3C)
T7398 000:368.260 JLINK_ReadMemEx(0x080001D4, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.263    -- Read from C cache (2 bytes @ 0x080001D4)
T7398 000:368.268   Data:  08 88
T7398 000:368.272 - 0.011ms returns 2 (0x2)
T7398 000:368.276 JLINK_ReadMemEx(0x080001D6, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.279    -- Read from C cache (2 bytes @ 0x080001D6)
T7398 000:368.283   Data:  4F F0
T7398 000:368.287 - 0.011ms returns 2 (0x2)
T7398 000:368.291 JLINK_ReadMemEx(0x080001D8, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.294    -- Read from C cache (60 bytes @ 0x080001D8)
T7398 000:368.298   Data:  00 00 80 F3 14 88 62 B6 61 B6 BF F3 4F 8F BF F3 ...
T7398 000:368.303 - 0.012ms returns 60 (0x3C)
T7398 000:368.306 JLINK_ReadMemEx(0x080001D8, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.309    -- Read from C cache (2 bytes @ 0x080001D8)
T7398 000:368.314   Data:  00 00
T7398 000:368.318 - 0.011ms returns 2 (0x2)
T7398 000:368.322 JLINK_ReadMemEx(0x080001DA, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.325    -- Read from C cache (2 bytes @ 0x080001DA)
T7398 000:368.329   Data:  80 F3
T7398 000:368.333 - 0.011ms returns 2 (0x2)
T7398 000:368.337 JLINK_ReadMemEx(0x080001DC, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.340    -- Read from C cache (60 bytes @ 0x080001DC)
T7398 000:368.345   Data:  14 88 62 B6 61 B6 BF F3 4F 8F BF F3 6F 8F 00 DF ...
T7398 000:368.349 - 0.012ms returns 60 (0x3C)
T7398 000:368.353 JLINK_ReadMemEx(0x080001DC, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.356    -- Read from C cache (2 bytes @ 0x080001DC)
T7398 000:368.360   Data:  14 88
T7398 000:368.364 - 0.011ms returns 2 (0x2)
T7398 000:368.368 JLINK_ReadMemEx(0x080001DE, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.371    -- Read from C cache (2 bytes @ 0x080001DE)
T7398 000:368.375   Data:  62 B6
T7398 000:368.379 - 0.011ms returns 2 (0x2)
T7398 000:368.383 JLINK_ReadMemEx(0x080001E0, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.386    -- Read from C cache (60 bytes @ 0x080001E0)
T7398 000:368.391   Data:  61 B6 BF F3 4F 8F BF F3 6F 8F 00 DF 00 BF 00 BF ...
T7398 000:368.395 - 0.012ms returns 60 (0x3C)
T7398 000:368.398 JLINK_ReadMemEx(0x080001E0, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.402    -- Read from C cache (2 bytes @ 0x080001E0)
T7398 000:368.406   Data:  61 B6
T7398 000:368.410 - 0.011ms returns 2 (0x2)
T7398 000:368.414 JLINK_ReadMemEx(0x080001E0, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.417    -- Read from C cache (60 bytes @ 0x080001E0)
T7398 000:368.422   Data:  61 B6 BF F3 4F 8F BF F3 6F 8F 00 DF 00 BF 00 BF ...
T7398 000:368.426 - 0.012ms returns 60 (0x3C)
T7398 000:368.429 JLINK_ReadMemEx(0x080001E0, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.432    -- Read from C cache (2 bytes @ 0x080001E0)
T7398 000:368.437   Data:  61 B6
T7398 000:368.441 - 0.011ms returns 2 (0x2)
T7398 000:368.445 JLINK_ReadMemEx(0x080001E2, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.448    -- Read from C cache (2 bytes @ 0x080001E2)
T7398 000:368.452   Data:  BF F3
T7398 000:368.456 - 0.011ms returns 2 (0x2)
T7398 000:368.460 JLINK_ReadMemEx(0x080001E2, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.463    -- Read from C cache (2 bytes @ 0x080001E2)
T7398 000:368.467   Data:  BF F3
T7398 000:368.471 - 0.011ms returns 2 (0x2)
T7398 000:368.475 JLINK_ReadMemEx(0x080001E4, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.479    -- Read from C cache (60 bytes @ 0x080001E4)
T7398 000:368.484   Data:  4F 8F BF F3 6F 8F 00 DF 00 BF 00 BF 08 ED 00 E0 ...
T7398 000:368.488 - 0.013ms returns 60 (0x3C)
T7398 000:368.492 JLINK_ReadMemEx(0x080001E4, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.495    -- Read from C cache (2 bytes @ 0x080001E4)
T7398 000:368.499   Data:  4F 8F
T7398 000:368.503 - 0.011ms returns 2 (0x2)
T7398 000:368.507 JLINK_ReadMemEx(0x080001E6, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.510    -- Read from C cache (2 bytes @ 0x080001E6)
T7398 000:368.515   Data:  BF F3
T7398 000:368.519 - 0.011ms returns 2 (0x2)
T7398 000:368.523 JLINK_ReadMemEx(0x080001E8, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.526    -- Read from C cache (60 bytes @ 0x080001E8)
T7398 000:368.530   Data:  6F 8F 00 DF 00 BF 00 BF 08 ED 00 E0 DF F8 0C 00 ...
T7398 000:368.534 - 0.012ms returns 60 (0x3C)
T7398 000:368.538 JLINK_ReadMemEx(0x080001E8, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.541    -- Read from C cache (2 bytes @ 0x080001E8)
T7398 000:368.545   Data:  6F 8F
T7398 000:368.550 - 0.011ms returns 2 (0x2)
T7398 000:368.553 JLINK_ReadMemEx(0x080001EA, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.556    -- Read from C cache (2 bytes @ 0x080001EA)
T7398 000:368.561   Data:  00 DF
T7398 000:368.565 - 0.011ms returns 2 (0x2)
T7398 000:368.569 JLINK_ReadMemEx(0x080001EC, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.572    -- Read from C cache (60 bytes @ 0x080001EC)
T7398 000:368.576   Data:  00 BF 00 BF 08 ED 00 E0 DF F8 0C 00 01 68 41 F4 ...
T7398 000:368.580 - 0.012ms returns 60 (0x3C)
T7398 000:368.584 JLINK_ReadMemEx(0x080001EC, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.587    -- Read from C cache (2 bytes @ 0x080001EC)
T7398 000:368.591   Data:  00 BF
T7398 000:368.596 - 0.011ms returns 2 (0x2)
T7398 000:368.599 JLINK_ReadMemEx(0x080001EC, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.602    -- Read from C cache (60 bytes @ 0x080001EC)
T7398 000:368.607   Data:  00 BF 00 BF 08 ED 00 E0 DF F8 0C 00 01 68 41 F4 ...
T7398 000:368.611 - 0.012ms returns 60 (0x3C)
T7398 000:368.615 JLINK_ReadMemEx(0x080001EC, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.618    -- Read from C cache (2 bytes @ 0x080001EC)
T7398 000:368.622   Data:  00 BF
T7398 000:368.627 - 0.011ms returns 2 (0x2)
T7398 000:368.630 JLINK_ReadMemEx(0x080001EE, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.633    -- Read from C cache (2 bytes @ 0x080001EE)
T7398 000:368.637   Data:  00 BF
T7398 000:368.642 - 0.011ms returns 2 (0x2)
T7398 000:368.646 JLINK_ReadMemEx(0x080001EE, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.649    -- Read from C cache (2 bytes @ 0x080001EE)
T7398 000:368.653   Data:  00 BF
T7398 000:368.657 - 0.011ms returns 2 (0x2)
T7398 000:368.661 JLINK_ReadMemEx(0x080001F0, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.664    -- Read from C cache (60 bytes @ 0x080001F0)
T7398 000:368.668   Data:  08 ED 00 E0 DF F8 0C 00 01 68 41 F4 70 01 01 60 ...
T7398 000:368.673 - 0.012ms returns 60 (0x3C)
T7398 000:368.676 JLINK_ReadMemEx(0x080001F0, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.679    -- Read from C cache (2 bytes @ 0x080001F0)
T7398 000:368.684   Data:  08 ED
T7398 000:368.688 - 0.011ms returns 2 (0x2)
T7398 000:368.692 JLINK_ReadMemEx(0x080001F4, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.695    -- Read from C cache (60 bytes @ 0x080001F4)
T7398 000:368.699   Data:  DF F8 0C 00 01 68 41 F4 70 01 01 60 70 47 00 BF ...
T7398 000:368.704 - 0.012ms returns 60 (0x3C)
T7398 000:368.707 JLINK_ReadMemEx(0x080001F4, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.710    -- Read from C cache (2 bytes @ 0x080001F4)
T7398 000:368.714   Data:  DF F8
T7398 000:368.719 - 0.011ms returns 2 (0x2)
T7398 000:368.723 JLINK_ReadMemEx(0x080001F6, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.726    -- Read from C cache (2 bytes @ 0x080001F6)
T7398 000:368.730   Data:  0C 00
T7398 000:368.734 - 0.011ms returns 2 (0x2)
T7398 000:368.738 JLINK_ReadMemEx(0x080001F8, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.755    -- Read from C cache (60 bytes @ 0x080001F8)
T7398 000:368.760   Data:  01 68 41 F4 70 01 01 60 70 47 00 BF 88 ED 00 E0 ...
T7398 000:368.764 - 0.026ms returns 60 (0x3C)
T7398 000:368.767 JLINK_ReadMemEx(0x080001F8, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.771    -- Read from C cache (2 bytes @ 0x080001F8)
T7398 000:368.775   Data:  01 68
T7398 000:368.779 - 0.011ms returns 2 (0x2)
T7398 000:368.783 JLINK_ReadMemEx(0x080001FA, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.786    -- Read from C cache (2 bytes @ 0x080001FA)
T7398 000:368.790   Data:  41 F4
T7398 000:368.794 - 0.011ms returns 2 (0x2)
T7398 000:368.798 JLINK_ReadMemEx(0x080001FA, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.801    -- Read from C cache (2 bytes @ 0x080001FA)
T7398 000:368.805   Data:  41 F4
T7398 000:368.810 - 0.011ms returns 2 (0x2)
T7398 000:368.813 JLINK_ReadMemEx(0x080001FC, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.817    -- Read from C cache (60 bytes @ 0x080001FC)
T7398 000:368.821   Data:  70 01 01 60 70 47 00 BF 88 ED 00 E0 EF F3 09 80 ...
T7398 000:368.825 - 0.012ms returns 60 (0x3C)
T7398 000:368.829 JLINK_ReadMemEx(0x080001FC, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.832    -- Read from C cache (2 bytes @ 0x080001FC)
T7398 000:368.836   Data:  70 01
T7398 000:368.841 - 0.011ms returns 2 (0x2)
T7398 000:368.844 JLINK_ReadMemEx(0x080001FE, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.847    -- Read from C cache (2 bytes @ 0x080001FE)
T7398 000:368.852   Data:  01 60
T7398 000:368.856 - 0.011ms returns 2 (0x2)
T7398 000:368.860 JLINK_ReadMemEx(0x08000200, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.863    -- Read from C cache (60 bytes @ 0x08000200)
T7398 000:368.867   Data:  70 47 00 BF 88 ED 00 E0 EF F3 09 80 BF F3 6F 8F ...
T7398 000:368.871 - 0.012ms returns 60 (0x3C)
T7398 000:368.875 JLINK_ReadMemEx(0x08000200, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.878    -- Read from C cache (2 bytes @ 0x08000200)
T7398 000:368.882   Data:  70 47
T7398 000:368.887 - 0.011ms returns 2 (0x2)
T7398 000:368.891 JLINK_ReadMemEx(0x08000200, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.899    -- Read from C cache (60 bytes @ 0x08000200)
T7398 000:368.904   Data:  70 47 00 BF 88 ED 00 E0 EF F3 09 80 BF F3 6F 8F ...
T7398 000:368.908 - 0.017ms returns 60 (0x3C)
T7398 000:368.912 JLINK_ReadMemEx(0x08000200, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.915    -- Read from C cache (2 bytes @ 0x08000200)
T7398 000:368.919   Data:  70 47
T7398 000:368.923 - 0.011ms returns 2 (0x2)
T7398 000:368.927 JLINK_ReadMemEx(0x08000202, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.930    -- Read from C cache (2 bytes @ 0x08000202)
T7398 000:368.934   Data:  00 BF
T7398 000:368.938 - 0.011ms returns 2 (0x2)
T7398 000:368.942 JLINK_ReadMemEx(0x08000202, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.945    -- Read from C cache (2 bytes @ 0x08000202)
T7398 000:368.949   Data:  00 BF
T7398 000:368.954 - 0.011ms returns 2 (0x2)
T7398 000:368.957 JLINK_ReadMemEx(0x08000204, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.961    -- Read from C cache (60 bytes @ 0x08000204)
T7398 000:368.965   Data:  88 ED 00 E0 EF F3 09 80 BF F3 6F 8F 13 4B 1A 68 ...
T7398 000:368.969 - 0.012ms returns 60 (0x3C)
T7398 000:368.973 JLINK_ReadMemEx(0x08000204, 0x2 Bytes, Flags = 0x02000000)
T7398 000:368.976    -- Read from C cache (2 bytes @ 0x08000204)
T7398 000:368.980   Data:  88 ED
T7398 000:368.984 - 0.011ms returns 2 (0x2)
T7398 000:368.988 JLINK_ReadMemEx(0x08000208, 0x3C Bytes, Flags = 0x02000000)
T7398 000:368.992   CPU_ReadMem(64 bytes @ 0x08000240)
T7398 000:370.052    -- Updating C cache (64 bytes @ 0x08000240)
T7398 000:370.060    -- Read from C cache (60 bytes @ 0x08000208)
T7398 000:370.064   Data:  EF F3 09 80 BF F3 6F 8F 13 4B 1A 68 1E F0 10 0F ...
T7398 000:370.069 - 1.080ms returns 60 (0x3C)
T7398 000:370.073 JLINK_ReadMemEx(0x08000208, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.077    -- Read from C cache (2 bytes @ 0x08000208)
T7398 000:370.083   Data:  EF F3
T7398 000:370.088 - 0.014ms returns 2 (0x2)
T7398 000:370.092 JLINK_ReadMemEx(0x0800020A, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.095    -- Read from C cache (2 bytes @ 0x0800020A)
T7398 000:370.100   Data:  09 80
T7398 000:370.104 - 0.011ms returns 2 (0x2)
T7398 000:370.109 JLINK_ReadMemEx(0x0800020C, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.112    -- Read from C cache (60 bytes @ 0x0800020C)
T7398 000:370.116   Data:  BF F3 6F 8F 13 4B 1A 68 1E F0 10 0F 08 BF 20 ED ...
T7398 000:370.121 - 0.012ms returns 60 (0x3C)
T7398 000:370.124 JLINK_ReadMemEx(0x0800020C, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.127    -- Read from C cache (2 bytes @ 0x0800020C)
T7398 000:370.131   Data:  BF F3
T7398 000:370.136 - 0.011ms returns 2 (0x2)
T7398 000:370.139 JLINK_ReadMemEx(0x0800020E, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.142    -- Read from C cache (2 bytes @ 0x0800020E)
T7398 000:370.147   Data:  6F 8F
T7398 000:370.151 - 0.011ms returns 2 (0x2)
T7398 000:370.155 JLINK_ReadMemEx(0x08000210, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.158    -- Read from C cache (60 bytes @ 0x08000210)
T7398 000:370.162   Data:  13 4B 1A 68 1E F0 10 0F 08 BF 20 ED 10 8A 20 E9 ...
T7398 000:370.167 - 0.011ms returns 60 (0x3C)
T7398 000:370.170 JLINK_ReadMemEx(0x08000210, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.173    -- Read from C cache (2 bytes @ 0x08000210)
T7398 000:370.177   Data:  13 4B
T7398 000:370.182 - 0.011ms returns 2 (0x2)
T7398 000:370.186 JLINK_ReadMemEx(0x08000212, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.189    -- Read from C cache (2 bytes @ 0x08000212)
T7398 000:370.193   Data:  1A 68
T7398 000:370.197 - 0.011ms returns 2 (0x2)
T7398 000:370.201 JLINK_ReadMemEx(0x08000212, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.204    -- Read from C cache (2 bytes @ 0x08000212)
T7398 000:370.208   Data:  1A 68
T7398 000:370.212 - 0.011ms returns 2 (0x2)
T7398 000:370.216 JLINK_ReadMemEx(0x08000214, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.219    -- Read from C cache (60 bytes @ 0x08000214)
T7398 000:370.224   Data:  1E F0 10 0F 08 BF 20 ED 10 8A 20 E9 F0 4F 10 60 ...
T7398 000:370.228 - 0.011ms returns 60 (0x3C)
T7398 000:370.231 JLINK_ReadMemEx(0x08000214, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.234    -- Read from C cache (2 bytes @ 0x08000214)
T7398 000:370.238   Data:  1E F0
T7398 000:370.243 - 0.011ms returns 2 (0x2)
T7398 000:370.247 JLINK_ReadMemEx(0x08000214, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.250    -- Read from C cache (60 bytes @ 0x08000214)
T7398 000:370.254   Data:  1E F0 10 0F 08 BF 20 ED 10 8A 20 E9 F0 4F 10 60 ...
T7398 000:370.258 - 0.012ms returns 60 (0x3C)
T7398 000:370.262 JLINK_ReadMemEx(0x08000214, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.265    -- Read from C cache (2 bytes @ 0x08000214)
T7398 000:370.269   Data:  1E F0
T7398 000:370.274 - 0.011ms returns 2 (0x2)
T7398 000:370.277 JLINK_ReadMemEx(0x08000216, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.280    -- Read from C cache (2 bytes @ 0x08000216)
T7398 000:370.285   Data:  10 0F
T7398 000:370.289 - 0.011ms returns 2 (0x2)
T7398 000:370.293 JLINK_ReadMemEx(0x08000218, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.296    -- Read from C cache (60 bytes @ 0x08000218)
T7398 000:370.300   Data:  08 BF 20 ED 10 8A 20 E9 F0 4F 10 60 09 B4 4F F0 ...
T7398 000:370.305 - 0.012ms returns 60 (0x3C)
T7398 000:370.308 JLINK_ReadMemEx(0x08000218, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.311    -- Read from C cache (2 bytes @ 0x08000218)
T7398 000:370.316   Data:  08 BF
T7398 000:370.320 - 0.011ms returns 2 (0x2)
T7398 000:370.323 JLINK_ReadMemEx(0x0800021A, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.327    -- Read from C cache (2 bytes @ 0x0800021A)
T7398 000:370.331   Data:  20 ED
T7398 000:370.335 - 0.011ms returns 2 (0x2)
T7398 000:370.339 JLINK_ReadMemEx(0x0800021A, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.342    -- Read from C cache (2 bytes @ 0x0800021A)
T7398 000:370.346   Data:  20 ED
T7398 000:370.350 - 0.011ms returns 2 (0x2)
T7398 000:370.355 JLINK_ReadMemEx(0x0800021C, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.358    -- Read from C cache (60 bytes @ 0x0800021C)
T7398 000:370.363   Data:  10 8A 20 E9 F0 4F 10 60 09 B4 4F F0 50 00 80 F3 ...
T7398 000:370.367 - 0.012ms returns 60 (0x3C)
T7398 000:370.371 JLINK_ReadMemEx(0x0800021C, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.374    -- Read from C cache (2 bytes @ 0x0800021C)
T7398 000:370.378   Data:  10 8A
T7398 000:370.382 - 0.011ms returns 2 (0x2)
T7398 000:370.394 JLINK_ReadMemEx(0x0800021E, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.397    -- Read from C cache (2 bytes @ 0x0800021E)
T7398 000:370.401   Data:  20 E9
T7398 000:370.405 - 0.011ms returns 2 (0x2)
T7398 000:370.409 JLINK_ReadMemEx(0x08000220, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.412    -- Read from C cache (60 bytes @ 0x08000220)
T7398 000:370.416   Data:  F0 4F 10 60 09 B4 4F F0 50 00 80 F3 11 88 BF F3 ...
T7398 000:370.420 - 0.011ms returns 60 (0x3C)
T7398 000:370.424 JLINK_ReadMemEx(0x08000220, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.427    -- Read from C cache (2 bytes @ 0x08000220)
T7398 000:370.431   Data:  F0 4F
T7398 000:370.436 - 0.011ms returns 2 (0x2)
T7398 000:370.440 JLINK_ReadMemEx(0x08000222, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.443    -- Read from C cache (2 bytes @ 0x08000222)
T7398 000:370.447   Data:  10 60
T7398 000:370.451 - 0.011ms returns 2 (0x2)
T7398 000:370.455 JLINK_ReadMemEx(0x08000224, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.458    -- Read from C cache (60 bytes @ 0x08000224)
T7398 000:370.463   Data:  09 B4 4F F0 50 00 80 F3 11 88 BF F3 4F 8F BF F3 ...
T7398 000:370.467 - 0.012ms returns 60 (0x3C)
T7398 000:370.471 JLINK_ReadMemEx(0x08000224, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.474    -- Read from C cache (2 bytes @ 0x08000224)
T7398 000:370.478   Data:  09 B4
T7398 000:370.482 - 0.011ms returns 2 (0x2)
T7398 000:370.486 JLINK_ReadMemEx(0x08000224, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.489    -- Read from C cache (60 bytes @ 0x08000224)
T7398 000:370.493   Data:  09 B4 4F F0 50 00 80 F3 11 88 BF F3 4F 8F BF F3 ...
T7398 000:370.498 - 0.012ms returns 60 (0x3C)
T7398 000:370.501 JLINK_ReadMemEx(0x08000224, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.505    -- Read from C cache (2 bytes @ 0x08000224)
T7398 000:370.509   Data:  09 B4
T7398 000:370.513 - 0.011ms returns 2 (0x2)
T7398 000:370.517 JLINK_ReadMemEx(0x08000226, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.520    -- Read from C cache (2 bytes @ 0x08000226)
T7398 000:370.524   Data:  4F F0
T7398 000:370.528 - 0.011ms returns 2 (0x2)
T7398 000:370.532 JLINK_ReadMemEx(0x08000226, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.535    -- Read from C cache (2 bytes @ 0x08000226)
T7398 000:370.539   Data:  4F F0
T7398 000:370.544 - 0.012ms returns 2 (0x2)
T7398 000:370.547 JLINK_ReadMemEx(0x08000228, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.551    -- Read from C cache (60 bytes @ 0x08000228)
T7398 000:370.555   Data:  50 00 80 F3 11 88 BF F3 4F 8F BF F3 6F 8F 1C F0 ...
T7398 000:370.559 - 0.012ms returns 60 (0x3C)
T7398 000:370.563 JLINK_ReadMemEx(0x08000228, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.566    -- Read from C cache (2 bytes @ 0x08000228)
T7398 000:370.570   Data:  50 00
T7398 000:370.575 - 0.011ms returns 2 (0x2)
T7398 000:370.578 JLINK_ReadMemEx(0x0800022A, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.581    -- Read from C cache (2 bytes @ 0x0800022A)
T7398 000:370.586   Data:  80 F3
T7398 000:370.590 - 0.011ms returns 2 (0x2)
T7398 000:370.594 JLINK_ReadMemEx(0x0800022C, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.597    -- Read from C cache (60 bytes @ 0x0800022C)
T7398 000:370.601   Data:  11 88 BF F3 4F 8F BF F3 6F 8F 1C F0 D3 FF 4F F0 ...
T7398 000:370.606 - 0.012ms returns 60 (0x3C)
T7398 000:370.609 JLINK_ReadMemEx(0x0800022C, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.612    -- Read from C cache (2 bytes @ 0x0800022C)
T7398 000:370.616   Data:  11 88
T7398 000:370.621 - 0.011ms returns 2 (0x2)
T7398 000:370.625 JLINK_ReadMemEx(0x0800022E, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.629    -- Read from C cache (2 bytes @ 0x0800022E)
T7398 000:370.633   Data:  BF F3
T7398 000:370.638 - 0.012ms returns 2 (0x2)
T7398 000:370.641 JLINK_ReadMemEx(0x08000230, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.644    -- Read from C cache (60 bytes @ 0x08000230)
T7398 000:370.649   Data:  4F 8F BF F3 6F 8F 1C F0 D3 FF 4F F0 00 00 80 F3 ...
T7398 000:370.653 - 0.012ms returns 60 (0x3C)
T7398 000:370.656 JLINK_ReadMemEx(0x08000230, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.660    -- Read from C cache (2 bytes @ 0x08000230)
T7398 000:370.664   Data:  4F 8F
T7398 000:370.668 - 0.011ms returns 2 (0x2)
T7398 000:370.672 JLINK_ReadMemEx(0x08000232, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.675    -- Read from C cache (2 bytes @ 0x08000232)
T7398 000:370.679   Data:  BF F3
T7398 000:370.683 - 0.011ms returns 2 (0x2)
T7398 000:370.687 JLINK_ReadMemEx(0x08000234, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.690    -- Read from C cache (60 bytes @ 0x08000234)
T7398 000:370.695   Data:  6F 8F 1C F0 D3 FF 4F F0 00 00 80 F3 11 88 09 BC ...
T7398 000:370.699 - 0.012ms returns 60 (0x3C)
T7398 000:370.703 JLINK_ReadMemEx(0x08000234, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.706    -- Read from C cache (2 bytes @ 0x08000234)
T7398 000:370.710   Data:  6F 8F
T7398 000:370.714 - 0.011ms returns 2 (0x2)
T7398 000:370.718 JLINK_ReadMemEx(0x08000236, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.721    -- Read from C cache (2 bytes @ 0x08000236)
T7398 000:370.725   Data:  1C F0
T7398 000:370.729 - 0.011ms returns 2 (0x2)
T7398 000:370.733 JLINK_ReadMemEx(0x08000238, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.736    -- Read from C cache (60 bytes @ 0x08000238)
T7398 000:370.741   Data:  D3 FF 4F F0 00 00 80 F3 11 88 09 BC 19 68 08 68 ...
T7398 000:370.745 - 0.012ms returns 60 (0x3C)
T7398 000:370.749 JLINK_ReadMemEx(0x08000238, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.752    -- Read from C cache (2 bytes @ 0x08000238)
T7398 000:370.756   Data:  D3 FF
T7398 000:370.760 - 0.011ms returns 2 (0x2)
T7398 000:370.764 JLINK_ReadMemEx(0x0800023A, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.767    -- Read from C cache (2 bytes @ 0x0800023A)
T7398 000:370.771   Data:  4F F0
T7398 000:370.775 - 0.011ms returns 2 (0x2)
T7398 000:370.779 JLINK_ReadMemEx(0x0800023C, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.782    -- Read from C cache (60 bytes @ 0x0800023C)
T7398 000:370.787   Data:  00 00 80 F3 11 88 09 BC 19 68 08 68 B0 E8 F0 4F ...
T7398 000:370.791 - 0.012ms returns 60 (0x3C)
T7398 000:370.795 JLINK_ReadMemEx(0x0800023C, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.798    -- Read from C cache (2 bytes @ 0x0800023C)
T7398 000:370.802   Data:  00 00
T7398 000:370.806 - 0.011ms returns 2 (0x2)
T7398 000:370.810 JLINK_ReadMemEx(0x0800023E, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.813    -- Read from C cache (2 bytes @ 0x0800023E)
T7398 000:370.817   Data:  80 F3
T7398 000:370.822 - 0.011ms returns 2 (0x2)
T7398 000:370.825 JLINK_ReadMemEx(0x08000240, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.828    -- Read from C cache (60 bytes @ 0x08000240)
T7398 000:370.833   Data:  11 88 09 BC 19 68 08 68 B0 E8 F0 4F 1E F0 10 0F ...
T7398 000:370.837 - 0.012ms returns 60 (0x3C)
T7398 000:370.841 JLINK_ReadMemEx(0x08000240, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.844    -- Read from C cache (2 bytes @ 0x08000240)
T7398 000:370.848   Data:  11 88
T7398 000:370.852 - 0.011ms returns 2 (0x2)
T7398 000:370.856 JLINK_ReadMemEx(0x08000242, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.859    -- Read from C cache (2 bytes @ 0x08000242)
T7398 000:370.868   Data:  09 BC
T7398 000:370.873 - 0.017ms returns 2 (0x2)
T7398 000:370.877 JLINK_ReadMemEx(0x08000244, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.880    -- Read from C cache (60 bytes @ 0x08000244)
T7398 000:370.884   Data:  19 68 08 68 B0 E8 F0 4F 1E F0 10 0F 08 BF B0 EC ...
T7398 000:370.889 - 0.012ms returns 60 (0x3C)
T7398 000:370.893 JLINK_ReadMemEx(0x08000244, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.897    -- Read from C cache (2 bytes @ 0x08000244)
T7398 000:370.901   Data:  19 68
T7398 000:370.905 - 0.012ms returns 2 (0x2)
T7398 000:370.909 JLINK_ReadMemEx(0x08000244, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.912    -- Read from C cache (60 bytes @ 0x08000244)
T7398 000:370.917   Data:  19 68 08 68 B0 E8 F0 4F 1E F0 10 0F 08 BF B0 EC ...
T7398 000:370.921 - 0.012ms returns 60 (0x3C)
T7398 000:370.924 JLINK_ReadMemEx(0x08000244, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.927    -- Read from C cache (2 bytes @ 0x08000244)
T7398 000:370.932   Data:  19 68
T7398 000:370.936 - 0.011ms returns 2 (0x2)
T7398 000:370.940 JLINK_ReadMemEx(0x08000246, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.943    -- Read from C cache (2 bytes @ 0x08000246)
T7398 000:370.947   Data:  08 68
T7398 000:370.951 - 0.011ms returns 2 (0x2)
T7398 000:370.955 JLINK_ReadMemEx(0x08000246, 0x2 Bytes, Flags = 0x02000000)
T7398 000:370.958    -- Read from C cache (2 bytes @ 0x08000246)
T7398 000:370.962   Data:  08 68
T7398 000:370.967 - 0.011ms returns 2 (0x2)
T7398 000:370.970 JLINK_ReadMemEx(0x08000248, 0x3C Bytes, Flags = 0x02000000)
T7398 000:370.975   CPU_ReadMem(64 bytes @ 0x08000280)
T7398 000:371.576    -- Updating C cache (64 bytes @ 0x08000280)
T7398 000:371.590    -- Read from C cache (60 bytes @ 0x08000248)
T7398 000:371.595   Data:  B0 E8 F0 4F 1E F0 10 0F 08 BF B0 EC 10 8A 80 F3 ...
T7398 000:371.600 - 0.629ms returns 60 (0x3C)
T7398 000:371.606 JLINK_ReadMemEx(0x08000248, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.611    -- Read from C cache (2 bytes @ 0x08000248)
T7398 000:371.615   Data:  B0 E8
T7398 000:371.620 - 0.013ms returns 2 (0x2)
T7398 000:371.626 JLINK_ReadMemEx(0x08000248, 0x3C Bytes, Flags = 0x02000000)
T7398 000:371.629    -- Read from C cache (60 bytes @ 0x08000248)
T7398 000:371.634   Data:  B0 E8 F0 4F 1E F0 10 0F 08 BF B0 EC 10 8A 80 F3 ...
T7398 000:371.638 - 0.012ms returns 60 (0x3C)
T7398 000:371.642 JLINK_ReadMemEx(0x08000248, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.645    -- Read from C cache (2 bytes @ 0x08000248)
T7398 000:371.649   Data:  B0 E8
T7398 000:371.653 - 0.011ms returns 2 (0x2)
T7398 000:371.657 JLINK_ReadMemEx(0x0800024A, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.660    -- Read from C cache (2 bytes @ 0x0800024A)
T7398 000:371.665   Data:  F0 4F
T7398 000:371.669 - 0.011ms returns 2 (0x2)
T7398 000:371.673 JLINK_ReadMemEx(0x0800024C, 0x3C Bytes, Flags = 0x02000000)
T7398 000:371.677    -- Read from C cache (60 bytes @ 0x0800024C)
T7398 000:371.681   Data:  1E F0 10 0F 08 BF B0 EC 10 8A 80 F3 09 88 BF F3 ...
T7398 000:371.685 - 0.012ms returns 60 (0x3C)
T7398 000:371.689 JLINK_ReadMemEx(0x0800024C, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.692    -- Read from C cache (2 bytes @ 0x0800024C)
T7398 000:371.696   Data:  1E F0
T7398 000:371.701 - 0.011ms returns 2 (0x2)
T7398 000:371.704 JLINK_ReadMemEx(0x0800024E, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.707    -- Read from C cache (2 bytes @ 0x0800024E)
T7398 000:371.711   Data:  10 0F
T7398 000:371.716 - 0.011ms returns 2 (0x2)
T7398 000:371.720 JLINK_ReadMemEx(0x08000250, 0x3C Bytes, Flags = 0x02000000)
T7398 000:371.723    -- Read from C cache (60 bytes @ 0x08000250)
T7398 000:371.727   Data:  08 BF B0 EC 10 8A 80 F3 09 88 BF F3 6F 8F 70 47 ...
T7398 000:371.732 - 0.012ms returns 60 (0x3C)
T7398 000:371.735 JLINK_ReadMemEx(0x08000250, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.738    -- Read from C cache (2 bytes @ 0x08000250)
T7398 000:371.743   Data:  08 BF
T7398 000:371.747 - 0.011ms returns 2 (0x2)
T7398 000:371.751 JLINK_ReadMemEx(0x08000252, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.754    -- Read from C cache (2 bytes @ 0x08000252)
T7398 000:371.758   Data:  B0 EC
T7398 000:371.762 - 0.011ms returns 2 (0x2)
T7398 000:371.766 JLINK_ReadMemEx(0x08000252, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.769    -- Read from C cache (2 bytes @ 0x08000252)
T7398 000:371.777   Data:  B0 EC
T7398 000:371.781 - 0.015ms returns 2 (0x2)
T7398 000:371.785 JLINK_ReadMemEx(0x08000254, 0x3C Bytes, Flags = 0x02000000)
T7398 000:371.788    -- Read from C cache (60 bytes @ 0x08000254)
T7398 000:371.793   Data:  10 8A 80 F3 09 88 BF F3 6F 8F 70 47 1C 00 00 20 ...
T7398 000:371.797 - 0.012ms returns 60 (0x3C)
T7398 000:371.801 JLINK_ReadMemEx(0x08000254, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.804    -- Read from C cache (2 bytes @ 0x08000254)
T7398 000:371.808   Data:  10 8A
T7398 000:371.812 - 0.011ms returns 2 (0x2)
T7398 000:371.816 JLINK_ReadMemEx(0x08000256, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.819    -- Read from C cache (2 bytes @ 0x08000256)
T7398 000:371.823   Data:  80 F3
T7398 000:371.828 - 0.011ms returns 2 (0x2)
T7398 000:371.831 JLINK_ReadMemEx(0x08000258, 0x3C Bytes, Flags = 0x02000000)
T7398 000:371.835    -- Read from C cache (60 bytes @ 0x08000258)
T7398 000:371.839   Data:  09 88 BF F3 6F 8F 70 47 1C 00 00 20 EF F3 05 80 ...
T7398 000:371.843 - 0.012ms returns 60 (0x3C)
T7398 000:371.847 JLINK_ReadMemEx(0x08000258, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.850    -- Read from C cache (2 bytes @ 0x08000258)
T7398 000:371.854   Data:  09 88
T7398 000:371.858 - 0.011ms returns 2 (0x2)
T7398 000:371.865 JLINK_ReadMemEx(0x0800025A, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.868    -- Read from C cache (2 bytes @ 0x0800025A)
T7398 000:371.873   Data:  BF F3
T7398 000:371.877 - 0.011ms returns 2 (0x2)
T7398 000:371.881 JLINK_ReadMemEx(0x0800025C, 0x3C Bytes, Flags = 0x02000000)
T7398 000:371.884    -- Read from C cache (60 bytes @ 0x0800025C)
T7398 000:371.888   Data:  6F 8F 70 47 1C 00 00 20 EF F3 05 80 70 47 00 00 ...
T7398 000:371.893 - 0.012ms returns 60 (0x3C)
T7398 000:371.896 JLINK_ReadMemEx(0x0800025C, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.899    -- Read from C cache (2 bytes @ 0x0800025C)
T7398 000:371.903   Data:  6F 8F
T7398 000:371.908 - 0.011ms returns 2 (0x2)
T7398 000:371.912 JLINK_ReadMemEx(0x0800025E, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.915    -- Read from C cache (2 bytes @ 0x0800025E)
T7398 000:371.919   Data:  70 47
T7398 000:371.923 - 0.011ms returns 2 (0x2)
T7398 000:371.927 JLINK_ReadMemEx(0x08000260, 0x3C Bytes, Flags = 0x02000000)
T7398 000:371.930    -- Read from C cache (60 bytes @ 0x08000260)
T7398 000:371.934   Data:  1C 00 00 20 EF F3 05 80 70 47 00 00 EF F3 08 80 ...
T7398 000:371.939 - 0.012ms returns 60 (0x3C)
T7398 000:371.942 JLINK_ReadMemEx(0x08000260, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.946    -- Read from C cache (2 bytes @ 0x08000260)
T7398 000:371.950   Data:  1C 00
T7398 000:371.954 - 0.011ms returns 2 (0x2)
T7398 000:371.958 JLINK_ReadMemEx(0x08000264, 0x3C Bytes, Flags = 0x02000000)
T7398 000:371.961    -- Read from C cache (60 bytes @ 0x08000264)
T7398 000:371.965   Data:  EF F3 05 80 70 47 00 00 EF F3 08 80 70 47 EF F3 ...
T7398 000:371.970 - 0.012ms returns 60 (0x3C)
T7398 000:371.973 JLINK_ReadMemEx(0x08000264, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.976    -- Read from C cache (2 bytes @ 0x08000264)
T7398 000:371.980   Data:  EF F3
T7398 000:371.985 - 0.011ms returns 2 (0x2)
T7398 000:371.988 JLINK_ReadMemEx(0x08000266, 0x2 Bytes, Flags = 0x02000000)
T7398 000:371.991    -- Read from C cache (2 bytes @ 0x08000266)
T7398 000:371.996   Data:  05 80
T7398 000:372.000 - 0.011ms returns 2 (0x2)
T7398 000:372.004 JLINK_ReadMemEx(0x08000268, 0x3C Bytes, Flags = 0x02000000)
T7398 000:372.007    -- Read from C cache (60 bytes @ 0x08000268)
T7398 000:372.011   Data:  70 47 00 00 EF F3 08 80 70 47 EF F3 09 80 70 47 ...
T7398 000:372.016 - 0.012ms returns 60 (0x3C)
T7398 000:372.019 JLINK_ReadMemEx(0x08000268, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.022    -- Read from C cache (2 bytes @ 0x08000268)
T7398 000:372.027   Data:  70 47
T7398 000:372.031 - 0.011ms returns 2 (0x2)
T7398 000:372.034 JLINK_ReadMemEx(0x0800026A, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.037    -- Read from C cache (2 bytes @ 0x0800026A)
T7398 000:372.043   Data:  00 00
T7398 000:372.047 - 0.013ms returns 2 (0x2)
T7398 000:372.051 JLINK_ReadMemEx(0x0800026A, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.054    -- Read from C cache (2 bytes @ 0x0800026A)
T7398 000:372.058   Data:  00 00
T7398 000:372.063 - 0.011ms returns 2 (0x2)
T7398 000:372.066 JLINK_ReadMemEx(0x0800026C, 0x3C Bytes, Flags = 0x02000000)
T7398 000:372.069    -- Read from C cache (60 bytes @ 0x0800026C)
T7398 000:372.074   Data:  EF F3 08 80 70 47 EF F3 09 80 70 47 68 46 70 47 ...
T7398 000:372.078 - 0.012ms returns 60 (0x3C)
T7398 000:372.082 JLINK_ReadMemEx(0x0800026C, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.085    -- Read from C cache (2 bytes @ 0x0800026C)
T7398 000:372.089   Data:  EF F3
T7398 000:372.093 - 0.011ms returns 2 (0x2)
T7398 000:372.097 JLINK_ReadMemEx(0x0800026C, 0x3C Bytes, Flags = 0x02000000)
T7398 000:372.100    -- Read from C cache (60 bytes @ 0x0800026C)
T7398 000:372.105   Data:  EF F3 08 80 70 47 EF F3 09 80 70 47 68 46 70 47 ...
T7398 000:372.109 - 0.012ms returns 60 (0x3C)
T7398 000:372.113 JLINK_ReadMemEx(0x0800026C, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.116    -- Read from C cache (2 bytes @ 0x0800026C)
T7398 000:372.120   Data:  EF F3
T7398 000:372.124 - 0.011ms returns 2 (0x2)
T7398 000:372.128 JLINK_ReadMemEx(0x0800026E, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.131    -- Read from C cache (2 bytes @ 0x0800026E)
T7398 000:372.135   Data:  08 80
T7398 000:372.139 - 0.011ms returns 2 (0x2)
T7398 000:372.143 JLINK_ReadMemEx(0x08000270, 0x3C Bytes, Flags = 0x02000000)
T7398 000:372.147    -- Read from C cache (60 bytes @ 0x08000270)
T7398 000:372.151   Data:  70 47 EF F3 09 80 70 47 68 46 70 47 06 48 80 47 ...
T7398 000:372.155 - 0.012ms returns 60 (0x3C)
T7398 000:372.159 JLINK_ReadMemEx(0x08000270, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.162    -- Read from C cache (2 bytes @ 0x08000270)
T7398 000:372.167   Data:  70 47
T7398 000:372.171 - 0.012ms returns 2 (0x2)
T7398 000:372.174 JLINK_ReadMemEx(0x08000272, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.178    -- Read from C cache (2 bytes @ 0x08000272)
T7398 000:372.182   Data:  EF F3
T7398 000:372.186 - 0.012ms returns 2 (0x2)
T7398 000:372.190 JLINK_ReadMemEx(0x08000272, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.193    -- Read from C cache (2 bytes @ 0x08000272)
T7398 000:372.197   Data:  EF F3
T7398 000:372.202 - 0.011ms returns 2 (0x2)
T7398 000:372.206 JLINK_ReadMemEx(0x08000274, 0x3C Bytes, Flags = 0x02000000)
T7398 000:372.209    -- Read from C cache (60 bytes @ 0x08000274)
T7398 000:372.213   Data:  09 80 70 47 68 46 70 47 06 48 80 47 06 48 00 47 ...
T7398 000:372.218 - 0.012ms returns 60 (0x3C)
T7398 000:372.221 JLINK_ReadMemEx(0x08000274, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.224    -- Read from C cache (2 bytes @ 0x08000274)
T7398 000:372.229   Data:  09 80
T7398 000:372.233 - 0.011ms returns 2 (0x2)
T7398 000:372.237 JLINK_ReadMemEx(0x08000276, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.240    -- Read from C cache (2 bytes @ 0x08000276)
T7398 000:372.244   Data:  70 47
T7398 000:372.248 - 0.011ms returns 2 (0x2)
T7398 000:372.252 JLINK_ReadMemEx(0x08000278, 0x3C Bytes, Flags = 0x02000000)
T7398 000:372.255    -- Read from C cache (60 bytes @ 0x08000278)
T7398 000:372.260   Data:  68 46 70 47 06 48 80 47 06 48 00 47 FE E7 FE E7 ...
T7398 000:372.264 - 0.012ms returns 60 (0x3C)
T7398 000:372.268 JLINK_ReadMemEx(0x08000278, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.271    -- Read from C cache (2 bytes @ 0x08000278)
T7398 000:372.275   Data:  68 46
T7398 000:372.279 - 0.011ms returns 2 (0x2)
T7398 000:372.283 JLINK_ReadMemEx(0x08000278, 0x3C Bytes, Flags = 0x02000000)
T7398 000:372.287    -- Read from C cache (60 bytes @ 0x08000278)
T7398 000:372.291   Data:  68 46 70 47 06 48 80 47 06 48 00 47 FE E7 FE E7 ...
T7398 000:372.295 - 0.012ms returns 60 (0x3C)
T7398 000:372.299 JLINK_ReadMemEx(0x08000278, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.302    -- Read from C cache (2 bytes @ 0x08000278)
T7398 000:372.306   Data:  68 46
T7398 000:372.312 - 0.012ms returns 2 (0x2)
T7398 000:372.316 JLINK_ReadMemEx(0x0800027A, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.319    -- Read from C cache (2 bytes @ 0x0800027A)
T7398 000:372.323   Data:  70 47
T7398 000:372.327 - 0.011ms returns 2 (0x2)
T7398 000:372.331 JLINK_ReadMemEx(0x0800027A, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.334    -- Read from C cache (2 bytes @ 0x0800027A)
T7398 000:372.338   Data:  70 47
T7398 000:372.342 - 0.011ms returns 2 (0x2)
T7398 000:372.346 JLINK_ReadMemEx(0x0800027C, 0x3C Bytes, Flags = 0x02000000)
T7398 000:372.349    -- Read from C cache (60 bytes @ 0x0800027C)
T7398 000:372.354   Data:  06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T7398 000:372.358 - 0.012ms returns 60 (0x3C)
T7398 000:372.362 JLINK_ReadMemEx(0x0800027C, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.365    -- Read from C cache (2 bytes @ 0x0800027C)
T7398 000:372.369   Data:  06 48
T7398 000:372.373 - 0.011ms returns 2 (0x2)
T7398 000:372.585 JLINK_ReadMemEx(0x0800027C, 0x3C Bytes, Flags = 0x02000000)
T7398 000:372.592    -- Read from C cache (60 bytes @ 0x0800027C)
T7398 000:372.597   Data:  06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T7398 000:372.601 - 0.016ms returns 60 (0x3C)
T7398 000:372.605 JLINK_ReadMemEx(0x0800027C, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.609    -- Read from C cache (2 bytes @ 0x0800027C)
T7398 000:372.613   Data:  06 48
T7398 000:372.617 - 0.012ms returns 2 (0x2)
T7398 000:372.621 JLINK_ReadMemEx(0x0800027E, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.624    -- Read from C cache (2 bytes @ 0x0800027E)
T7398 000:372.628   Data:  80 47
T7398 000:372.632 - 0.011ms returns 2 (0x2)
T7398 000:372.638 JLINK_ReadMemEx(0x0800027E, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.642    -- Read from C cache (2 bytes @ 0x0800027E)
T7398 000:372.646   Data:  80 47
T7398 000:372.650 - 0.011ms returns 2 (0x2)
T7398 000:372.654 JLINK_ReadMemEx(0x08000280, 0x3C Bytes, Flags = 0x02000000)
T7398 000:372.657    -- Read from C cache (60 bytes @ 0x08000280)
T7398 000:372.661   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T7398 000:372.666 - 0.012ms returns 60 (0x3C)
T7398 000:372.669 JLINK_ReadMemEx(0x08000280, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.673    -- Read from C cache (2 bytes @ 0x08000280)
T7398 000:372.677   Data:  06 48
T7398 000:372.681 - 0.011ms returns 2 (0x2)
T7398 000:372.686 JLINK_ReadMemEx(0x08000280, 0x3C Bytes, Flags = 0x02000000)
T7398 000:372.689    -- Read from C cache (60 bytes @ 0x08000280)
T7398 000:372.694   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T7398 000:372.698 - 0.012ms returns 60 (0x3C)
T7398 000:372.701 JLINK_ReadMemEx(0x08000280, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.704    -- Read from C cache (2 bytes @ 0x08000280)
T7398 000:372.708   Data:  06 48
T7398 000:372.713 - 0.011ms returns 2 (0x2)
T7398 000:372.716 JLINK_ReadMemEx(0x08000282, 0x2 Bytes, Flags = 0x02000000)
T7398 000:372.720    -- Read from C cache (2 bytes @ 0x08000282)
T7398 000:372.724   Data:  00 47
T7398 000:372.728 - 0.011ms returns 2 (0x2)
