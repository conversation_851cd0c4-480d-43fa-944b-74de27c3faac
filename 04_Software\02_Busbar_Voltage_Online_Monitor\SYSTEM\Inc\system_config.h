/*
 * @Author: quickly <EMAIL>
 * @Date: 2025-04-27 16:16:55
 * @LastEditors: quickly <EMAIL>
 * @LastEditTime: 2025-04-27 16:18:47
 * @FilePath: \EIDE_PRJe:\Company_Products_STM32_Program\company_products_program\Busbar_Voltage_Online_Monitor\SYSTEM\Inc\system_config.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/******************************************************************************
 * @file system_config.h
 * @brief 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-04-27
 * 
 * @copyright Copyright (c) 2025 
 * 
 * Processing flow:
 * 
 * @par dependencies
 * 
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/
#ifndef __SYSTEM_CONFIG_H
#define __SYSTEM_CONFIG_H


//******************************** Includes *********************************//
/* 1. C标准库头文件 */


/* 2. MCU头文件 */


/* 3. OS层头文件 */


/* 4. BSP驱动层头文件*/


/* 5. 应用层头文件 */


/* 6. 中间件头文件 */


//******************************** Includes *********************************//

//******************************** Defines **********************************//
/*
 * =============================================================================
 * 调试宏定义集中管理区域
 * =============================================================================
 * 说明：本区域统一管理整个项目的调试宏定义，便于集中控制各模块的日志输出
 * 使用方法：通过注释/取消注释相应的宏定义来启用/禁用对应模块的调试输出
 * =============================================================================
 */

/* 全局调试总开关 */
#define DEBUG_MODULE

#ifdef DEBUG_MODULE

/* =============================================================================
 * 系统层调试宏定义
 * =============================================================================
 */
/* 系统适配层调试 */
#define DEBUG_SYSTEM_ADAPTION

/* =============================================================================
 * 任务模块调试宏定义
 * =============================================================================
 */
/* FFT计算任务调试 */
#define DEBUG_FFT_CALCU_TASK

/* 数据处理任务调试 */
#define DEBUG_DATA_PROCESS_TASK

/* =============================================================================
 * BSP层调试宏定义
 * =============================================================================
 */
/* AD7606驱动调试 */
#define ADC_COLLECT_DEBUG
#define AD7606_DRIVER_DEBUG

/* LED处理器调试 */
// #define LED_HANDLER_DEBUG

/* SPI驱动调试 */
#define MCU_SPI_DRIVER_DEBUG

/* =============================================================================
 * 中间件调试宏定义
 * =============================================================================
 */
/* Modbus RTU调试 */
#define MODBUS_RTU_DEBUG

/* LPF FIR算法调试 */
// #define DEBUG_LPF_FIR_TASK

/* =============================================================================
 * 通信模块调试宏定义
 * =============================================================================
 */
/* RS485事件处理器调试 */
#define DEBUG_RS485_EVENT_HANDLER

/* RS485新处理器调试 */
// #define DEBUG_RS485_HANDLER_NEW

#endif /* DEBUG_MODULE */

//******************************** Defines **********************************//

#define BUSBAR_VOLTAGE_PROCESS
#define SOFTWARE_VERSION "V1.0.0"
#define HARDWARE_VERSION "V1.0.0"
#define DEFAULT_CALIBRATION_DATA 1.0f
#define USE_DYNAMIC_ALLOCATION
//******************************** Declaration ******************************//
/* 结构体定义 */
//******************************** Declaration ******************************//

//******************************** Variables ********************************//


//******************************** Variables ********************************//

//******************************** Functions ********************************//


//******************************** Functions ********************************//

#endif /* __SYSTEM_CONFIG_H__ */




